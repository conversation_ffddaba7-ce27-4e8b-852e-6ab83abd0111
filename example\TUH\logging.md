PS E:\code\HOA\XiAlphaHOSA\data\TUH> matlab -batch "test_eeg_loading"
=== EEG Loading and Comparison Test ===

TEST 1: Loading TUH format EEG data
=====================================
Warning: Function bitshift has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict. 
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function full has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function isequal has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function isscalar has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function isvector has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function numel has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function set has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function setstr has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function sparse has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function speye has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function strcmpi has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function strfind has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function strncmp has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function strncmpi has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function text has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function transpose has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function all has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function any has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function diff has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function erfinv has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function filter has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function findstr has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function isfinite has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function realmax has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function realmin has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function sign has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function labindex has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Function numlabs has the same name as a MATLAB built-in. We suggest you rename the function to avoid a potential name conflict.
> In path (line 109)
In addpath>doPathAddition (line 126)
In addpath (line 90)
In load_tuh_eeg (line 25)
In test_eeg_loading (line 19)
Warning: Either you added the EEGLAB path with subfolders or the EEGLAB path is in the MATLAB toolbox
         folder which is not recommended. You may experience errors if a plugin overloads a MATLAB function.
         If you have added path with subfolders, remove all the EEGLAB path except the root EEGLAB path.
         If EEGLAB is in the toolbox software, move it somewhere else. Then restart MATLAB and EEGLAB.

Warning:

Path Warning: It appears that you have added the path to all of the
subfolders to EEGLAB. This may create issues with some EEGLAB extensions
If EEGLAB cannot start or your experience a large number of warning
messages, remove all the EEGLAB paths then go to the EEGLAB folder
and start EEGLAB which will add all the necessary paths.


Warning: colordef will be removed in a future release. 
eeglab: options file is C:\Users\<USER>\eeg_options.m
Retrieving plugin versions from server...
Retrieving download statistics...
EEGLAB: adding "Biosig" v3.8.4 to the path
EEGLAB: adding "EEG-BIDS" v? (see >> help eegplugin_eegbids) - new version 10.2 available
EEGLAB: adding "ICLabel" v? (see >> help eegplugin_iclabel) - new version 1.6 available (critical bug fix)
EEGLAB: adding "bva-io" v? (see >> help eegplugin_bva_io) - new version 1.73 available
EEGLAB: adding "clean_rawdata" v? (see >> help eegplugin_clean_rawdata) - new version 2.10 available
EEGLAB: adding "dipfit" v? (see >> help eegplugin_dipfit) - new version 5.5 available
EEGLAB: adding "firfilt" v? (see >> help eegplugin_firfilt) - new version 2.8 available
Loading TUH EEG file: aaaaaaac_s001_t000.edf
sopen mode is "OVERFLOWDETECTION:OFF"
Reading data in EDF format...
eeg_checkset note: upper time limit (xmax) adjusted so (xmax-xmin)*srate+1 = number of frames
Detected/removing 'EEG' prefix from channel labels
Extracting events from last EEG channel...
Empty last channel is likely a data channel not event channel
Warning: no event found. Events might be embedded in a data channel.
         To extract events, use menu File > Import Event Info > From data channel
Successfully loaded TUH EEG data:
  Subject: aaaaaaac
  Session: 1
  Run: 0
  Channels: 33
  Samples: 75250
  Sampling rate: 250.00 Hz
  Duration: 301.00 seconds
? TUH loading successful

TEST 2: Loading BIDS format EEG data
====================================
Warning:

Path Warning: It appears that you have added the path to all of the
subfolders to EEGLAB. This may create issues with some EEGLAB extensions
If EEGLAB cannot start or your experience a large number of warning
messages, remove all the EEGLAB paths then go to the EEGLAB folder
and start EEGLAB which will add all the necessary paths.


Warning: colordef will be removed in a future release. 
eeglab: options file is C:\Users\<USER>\eeg_options.m
Retrieving plugin versions from server...
Retrieving download statistics...
EEGLAB: adding "Biosig" v3.8.4 to the path
EEGLAB: adding "EEG-BIDS" v? (see >> help eegplugin_eegbids) - new version 10.2 available
EEGLAB: adding "ICLabel" v? (see >> help eegplugin_iclabel) - new version 1.6 available (critical bug fix)
EEGLAB: adding "bva-io" v? (see >> help eegplugin_bva_io) - new version 1.73 available
EEGLAB: adding "clean_rawdata" v? (see >> help eegplugin_clean_rawdata) - new version 2.10 available
EEGLAB: adding "dipfit" v? (see >> help eegplugin_dipfit) - new version 5.5 available
EEGLAB: adding "firfilt" v? (see >> help eegplugin_firfilt) - new version 2.8 available
Loading BIDS EEG file: sub-000_ses-001_task-szMonitoring_run-01_eeg.edf
sopen mode is "OVERFLOWDETECTION:OFF"
Reading data in EDF format...
eeg_checkset note: upper time limit (xmax) adjusted so (xmax-xmin)*srate+1 = number of frames
Extracting events from last EEG channel...
Warning: event extraction failure, the last channel contains data
Warning: no event found. Events might be embedded in a data channel.
         To extract events, use menu File > Import Event Info > From data channel
Loading events from: sub-000_ses-001_task-szMonitoring_run-01_events.tsv
Loaded 8 events
  bckg: 7 events
  sz_foc_ia: 1 events
Successfully loaded BIDS EEG data:
  Subject: 000
  Session: 1
  Task: szMonitoring
  Run: 1
  Channels: 19
  Samples: 77056
  Sampling rate: 256.00 Hz
  Duration: 301.00 seconds
? BIDS loading successful

TEST 3: Comparing TUH vs BIDS consistency
=========================================
=== TUH vs BIDS EEG Data Consistency Check ===

Loading TUH format data...
Warning:

Path Warning: It appears that you have added the path to all of the
subfolders to EEGLAB. This may create issues with some EEGLAB extensions
If EEGLAB cannot start or your experience a large number of warning
messages, remove all the EEGLAB paths then go to the EEGLAB folder
and start EEGLAB which will add all the necessary paths.


Warning: colordef will be removed in a future release. 
eeglab: options file is C:\Users\<USER>\eeg_options.m
Retrieving plugin versions from server...
Retrieving download statistics...
EEGLAB: adding "Biosig" v3.8.4 to the path
EEGLAB: adding "EEG-BIDS" v? (see >> help eegplugin_eegbids) - new version 10.2 available
EEGLAB: adding "ICLabel" v? (see >> help eegplugin_iclabel) - new version 1.6 available (critical bug fix)
EEGLAB: adding "bva-io" v? (see >> help eegplugin_bva_io) - new version 1.73 available
EEGLAB: adding "clean_rawdata" v? (see >> help eegplugin_clean_rawdata) - new version 2.10 available
EEGLAB: adding "dipfit" v? (see >> help eegplugin_dipfit) - new version 5.5 available
EEGLAB: adding "firfilt" v? (see >> help eegplugin_firfilt) - new version 2.8 available
Loading TUH EEG file: aaaaaaac_s001_t000.edf
sopen mode is "OVERFLOWDETECTION:OFF"
Reading data in EDF format...
eeg_checkset note: upper time limit (xmax) adjusted so (xmax-xmin)*srate+1 = number of frames
Detected/removing 'EEG' prefix from channel labels
Extracting events from last EEG channel...
Empty last channel is likely a data channel not event channel
Warning: no event found. Events might be embedded in a data channel.
         To extract events, use menu File > Import Event Info > From data channel
Successfully loaded TUH EEG data:
  Subject: aaaaaaac
  Session: 1
  Run: 0
  Channels: 33
  Samples: 75250
  Sampling rate: 250.00 Hz
  Duration: 301.00 seconds

Loading BIDS format data...
Warning:

Path Warning: It appears that you have added the path to all of the
subfolders to EEGLAB. This may create issues with some EEGLAB extensions
If EEGLAB cannot start or your experience a large number of warning
messages, remove all the EEGLAB paths then go to the EEGLAB folder
and start EEGLAB which will add all the necessary paths.


Warning: colordef will be removed in a future release. 
eeglab: options file is C:\Users\<USER>\eeg_options.m
Retrieving plugin versions from server...
Retrieving download statistics...
EEGLAB: adding "Biosig" v3.8.4 to the path
EEGLAB: adding "EEG-BIDS" v? (see >> help eegplugin_eegbids) - new version 10.2 available
EEGLAB: adding "ICLabel" v? (see >> help eegplugin_iclabel) - new version 1.6 available (critical bug fix)
EEGLAB: adding "bva-io" v? (see >> help eegplugin_bva_io) - new version 1.73 available
EEGLAB: adding "clean_rawdata" v? (see >> help eegplugin_clean_rawdata) - new version 2.10 available
EEGLAB: adding "dipfit" v? (see >> help eegplugin_dipfit) - new version 5.5 available
EEGLAB: adding "firfilt" v? (see >> help eegplugin_firfilt) - new version 2.8 available
Loading BIDS EEG file: sub-000_ses-001_task-szMonitoring_run-01_eeg.edf
sopen mode is "OVERFLOWDETECTION:OFF"
Reading data in EDF format...
eeg_checkset note: upper time limit (xmax) adjusted so (xmax-xmin)*srate+1 = number of frames
Extracting events from last EEG channel...
Warning: event extraction failure, the last channel contains data
Warning: no event found. Events might be embedded in a data channel.
         To extract events, use menu File > Import Event Info > From data channel
Loading events from: sub-000_ses-001_task-szMonitoring_run-01_events.tsv
Loaded 8 events
  bckg: 7 events
  sz_foc_ia: 1 events
Successfully loaded BIDS EEG data:
  Subject: 000
  Session: 1
  Task: szMonitoring
  Run: 1
  Channels: 19
  Samples: 77056
  Sampling rate: 256.00 Hz
  Duration: 301.00 seconds

=== COMPARISON RESULTS ===

1. BASIC PROPERTIES:
   Channels: TUH=33, BIDS=19 [DIFFER]
   Samples: TUH=75250, BIDS=77056 [DIFFER]
   Sampling Rate: TUH=250.00 Hz, BIDS=256.00 Hz [DIFFER]
   Duration: TUH=301.00 s, BIDS=301.00 s [MATCH]

2. CHANNEL LABELS:
   Channel labels match: NO
   TUH channels (33): FP1-LE, FP2-LE, F3-LE, F4-LE, C3-LE, C4-LE, A1-LE, A2-LE, P3-LE, P4-LE, O1-LE, O2-LE, F7-LE, F8-LE, T3-LE, T4-LE, T5-LE, T6-LE, FZ-LE, CZ-LE, PZ-LE, OZ-LE, PG1-LE, PG2-LE, EKG-LE, SP2-LE, SP1-LE, RLC-LE, LUC-LE, 30-LE, T1-LE, T2-LE, PHOTIC PH  
   BIDS channels (19): FP1-Avg, F3-Avg, C3-Avg, P3-Avg, O1-Avg, F7-Avg, T3-Avg, T5-Avg, FZ-Avg, CZ-Avg, PZ-Avg, FP2-Avg, F4-Avg, C4-Avg, P4-Avg, O2-Avg, F8-Avg, T4-Avg, T6-Avg
   Channels only in TUH: 30-LE, A1-LE, A2-LE, C3-LE, C4-LE, CZ-LE, EKG-LE, F3-LE, F4-LE, F7-LE, F8-LE, FP1-LE, FP2-LE, FZ-LE, LUC-LE, O1-LE, O2-LE, OZ-LE, P3-LE, P4-LE, PG1-LE, PG2-LE, PHOTIC PH, PZ-LE, RLC-LE, SP1-LE, SP2-LE, T1-LE, T2-LE, T3-LE, T4-LE, T5-LE, T6-LE
   Channels only in BIDS: C3-Avg, C4-Avg, CZ-Avg, F3-Avg, F4-Avg, F7-Avg, F8-Avg, FP1-Avg, FP2-Avg, FZ-Avg, O1-Avg, O2-Avg, P3-Avg, P4-Avg, PZ-Avg, T3-Avg, T4-Avg, T5-Avg, T6-Avg

3. DATA VALUES:
   Cannot compare data values - dimension mismatch

4. EVENTS:
   TUH has events: NO
   BIDS has events: YES (8 events)

=== OVERALL ASSESSMENT ===
Overall consistency: INCONSISTENT
? The datasets show significant differences

Comparison completed.
? Consistency comparison completed

=== SUMMARY ===
TUH Dataset:
  File: aaaaaaac_s001_t000.edf
  Subject: aaaaaaac
  Channels: 33
  Duration: 301.00 seconds

BIDS Dataset:
  File: sub-000_ses-001_task-szMonitoring_run-01_eeg.edf
  Subject: 000
  Task: szMonitoring
  Channels: 19
  Duration: 301.00 seconds
  Events: 8

Consistency: INCONSISTENT

All tests completed successfully!

=== CHANNEL INFORMATION ===
TUH Channels: FP1-LE, FP2-LE, F3-LE, F4-LE, C3-LE, C4-LE, A1-LE, A2-LE, P3-LE, P4-LE, O1-LE, O2-LE, F7-LE, F8-LE, T3-LE, T4-LE, T5-LE, T6-LE, FZ-LE, CZ-LE, PZ-LE, OZ-LE, PG1-LE, PG2-LE, EKG-LE, SP2-LE, SP1-LE, RLC-LE, LUC-LE, 30-LE, T1-LE, T2-LE, PHOTIC PH
BIDS Channels: FP1-Avg, F3-Avg, C3-Avg, P3-Avg, O1-Avg, F7-Avg, T3-Avg, T5-Avg, FZ-Avg, CZ-Avg, PZ-Avg, FP2-Avg, F4-Avg, C4-Avg, P4-Avg, O2-Avg, F8-Avg, T4-Avg, T6-Avg

=== EVENT INFORMATION ===
Event types found in BIDS data:
  bckg: 7 events
  sz_foc_ia: 1 events

First 5 events:
  Event 1: bckg at 0.00 seconds
  Event 2: sz_foc_ia at 36.89 seconds
  Event 3: bckg at 137.46 seconds
  Event 4: bckg at 160.25 seconds
  Event 5: bckg at 183.31 seconds