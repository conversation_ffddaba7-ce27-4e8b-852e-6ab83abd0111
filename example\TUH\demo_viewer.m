%% De<PERSON>t for EEG Multichannel Viewer
% This script demonstrates how to use the EEG multichannel viewer
% with both TUH and BIDS format data

clear; clc;

% Define file paths
tuh_file = 'aaaaaaac_s001_t000.edf';
bids_file = 'sub-000_ses-001_task-szMonitoring_run-01_eeg.edf';
eeglab_path = 'E:\code\tools\eeglab2025.0.0\';

fprintf('=== EEG Multichannel Viewer Demo ===\n\n');

%% Load BIDS data (recommended for viewing due to events)
fprintf('Loading BIDS EEG data for viewing...\n');
try
    EEG_BIDS = load_bids_eeg(bids_file, eeglab_path);
    fprintf('✓ BIDS data loaded successfully\n\n');
catch ME
    fprintf('✗ Failed to load BIDS data: %s\n', ME.message);
    return;
end

%% Demo 1: Basic viewer with default settings
fprintf('Demo 1: Basic viewer with default settings\n');
fprintf('Press any key to continue...\n');
pause;

eeg_multichannel_viewer(EEG_BIDS);

fprintf('\nTry the interactive controls:\n');
fprintf('  ← → : Navigate through time\n');
fprintf('  + - : Zoom in/out\n');
fprintf('  ↑ ↓ : Scale amplitude\n');
fprintf('  r   : Reset view\n');
fprintf('  f   : Toggle filter\n');
fprintf('  g   : Toggle grid\n');
fprintf('  e   : Toggle events\n\n');

fprintf('Close the viewer window and press any key to continue...\n');
pause;

%% Demo 2: Viewer with custom settings - focus on seizure event
fprintf('Demo 2: Focused view on seizure event (starts at ~37 seconds)\n');
fprintf('Press any key to continue...\n');
pause;

% Focus on the seizure event which starts at 36.89 seconds
eeg_multichannel_viewer(EEG_BIDS, ...
                       'StartTime', 30, ...        % Start 30 seconds in
                       'TimeWindow', 20, ...       % Show 20 second window
                       'AmplitudeScale', 2, ...    % Increase amplitude
                       'ShowEvents', true, ...     % Show events
                       'ShowGrid', true, ...       % Show grid
                       'Title', 'BIDS EEG - Seizure Focus View');

fprintf('Notice the red seizure event marker around 37 seconds!\n');
fprintf('Close the viewer window and press any key to continue...\n');
pause;

%% Demo 3: Filtered view
fprintf('Demo 3: Filtered view (1-50 Hz bandpass)\n');
fprintf('Press any key to continue...\n');
pause;

eeg_multichannel_viewer(EEG_BIDS, ...
                       'StartTime', 0, ...
                       'TimeWindow', 15, ...
                       'FilterData', true, ...     % Apply filtering
                       'AmplitudeScale', 1.5, ...
                       'Title', 'BIDS EEG - Filtered View');

fprintf('Notice the cleaner signal with filtering applied!\n');
fprintf('Close the viewer window and press any key to continue...\n');
pause;

%% Demo 4: Compare with TUH data (if you want to see the difference)
fprintf('Demo 4: Load and view TUH data for comparison\n');
fprintf('Press any key to continue...\n');
pause;

try
    EEG_TUH = load_tuh_eeg(tuh_file, eeglab_path);
    
    eeg_multichannel_viewer(EEG_TUH, ...
                           'StartTime', 0, ...
                           'TimeWindow', 10, ...
                           'AmplitudeScale', 0.5, ... % Smaller scale due to more channels
                           'ShowEvents', false, ...   % TUH has no events
                           'Title', 'TUH EEG - Original Format (33 channels)');
    
    fprintf('Notice the difference: 33 channels vs 19 channels in BIDS!\n');
    fprintf('TUH includes EKG and reference channels.\n');
    
catch ME
    fprintf('Could not load TUH data: %s\n', ME.message);
end

fprintf('\n=== Demo completed! ===\n');
fprintf('You can now use the viewer function with your own data:\n');
fprintf('  eeg_multichannel_viewer(EEG_BIDS);\n');
fprintf('  eeg_multichannel_viewer(EEG_BIDS, ''StartTime'', 30, ''TimeWindow'', 15);\n');

%% Optional: Show event summary
if isfield(EEG_BIDS, 'event') && ~isempty(EEG_BIDS.event)
    fprintf('\n=== Event Summary ===\n');
    for i = 1:length(EEG_BIDS.event)
        event_time = (EEG_BIDS.event(i).latency - 1) / EEG_BIDS.srate;
        fprintf('Event %d: %s at %.2f seconds\n', i, EEG_BIDS.event(i).type, event_time);
    end
end
