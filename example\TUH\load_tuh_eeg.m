function EEG = load_tuh_eeg(filepath, eeglab_path)
% LOAD_TUH_EEG Load TUH format EEG data and return EEGLAB structure
%
% Usage:
%   EEG = load_tuh_eeg(filepath, eeglab_path)
%
% Inputs:
%   filepath    - Full path to the TUH .edf file (e.g., 'aaaaaaac_s001_t000.edf')
%   eeglab_path - Path to EEGLAB installation (optional, default: 'E:\code\tools\eeglab2025.0.0\')
%
% Outputs:
%   EEG         - EEGLAB structure containing the loaded EEG data
%
% Example:
%   EEG = load_tuh_eeg('aaaaaaac_s001_t000.edf');
%   EEG = load_tuh_eeg('aaaaaaac_s001_t000.edf', 'E:\code\tools\eeglab2025.0.0\');

% Set default EEGLAB path if not provided
if nargin < 2 || isempty(eeglab_path)
    eeglab_path = 'E:\code\tools\eeglab2025.0.0\';
end

% Add EEGLAB to path if not already added
if ~exist('eeglab', 'file')
    addpath(genpath(eeglab_path));
end

% Initialize EEGLAB (suppress GUI)
if ~exist('ALLEEG', 'var')
    eeglab('nogui');
end

% Check if file exists
if ~exist(filepath, 'file')
    error('File not found: %s', filepath);
end

try
    % Load EDF file using EEGLAB's pop_biosig function or alternative methods
    fprintf('Loading TUH EEG file: %s\n', filepath);

    % Try different methods to load EDF file
    biosig_available = false;
    try
        % Test if pop_biosig works
        if exist('pop_biosig', 'file')
            % Try a quick test to see if biosig extension is actually available
            test_result = which('pop_biosig');
            if ~isempty(test_result)
                biosig_available = true;
            end
        end
    catch
        biosig_available = false;
    end

    if biosig_available
        try
            % Method 1: Use pop_biosig (requires Biosig extension)
            EEG = pop_biosig(filepath);
        catch ME
            if contains(ME.message, 'Biosig')
                fprintf('Biosig extension not available, using basic EDF reader...\n');
                biosig_available = false;
            else
                rethrow(ME);
            end
        end
    end

    if ~biosig_available
        if exist('pop_readedf', 'file')
            % Method 2: Use pop_readedf (built-in EEGLAB function)
            try
                EEG = pop_readedf(filepath);
            catch
                % If pop_readedf fails, use basic reader
                fprintf('pop_readedf failed, using basic EDF reader...\n');
                [data, header] = read_edf_basic(filepath);
                EEG = create_eeglab_structure(data, header);
            end
        else
            % Method 3: Use basic EDF reader and create EEGLAB structure manually
            fprintf('Using basic EDF reader...\n');
            [data, header] = read_edf_basic(filepath);
            EEG = create_eeglab_structure(data, header);
        end
    end

    % Extract subject information from TUH filename
    [~, filename, ~] = fileparts(filepath);

    % Parse TUH filename format: aaaaaaac_s001_t000
    % where: aaaaaaac = subject ID, s001 = session, t000 = segment/trial
    parts = split(filename, '_');
    if length(parts) >= 3
        subject_id = parts{1};
        session_str = parts{2};
        trial_str = parts{3};

        % Extract session number (remove 's' prefix)
        if startsWith(session_str, 's')
            session_num = str2double(session_str(2:end));
        else
            session_num = 1;
        end

        % Extract trial/segment number (remove 't' prefix)
        if startsWith(trial_str, 't')
            trial_num = str2double(trial_str(2:end));
        else
            trial_num = 1;
        end

        % Set EEGLAB structure fields
        EEG.subject = subject_id;
        EEG.session = session_num;
        EEG.run = trial_num;
        EEG.task = 'szMonitoring'; % Default task for TUH data

        % Set dataset name
        EEG.setname = sprintf('TUH_%s_ses-%03d_run-%02d', subject_id, session_num, trial_num);
    else
        warning('Could not parse TUH filename format: %s', filename);
        EEG.subject = filename;
        EEG.session = 1;
        EEG.run = 1;
        EEG.task = 'szMonitoring';
        EEG.setname = sprintf('TUH_%s', filename);
    end

    % Add metadata
    EEG.format = 'TUH';
    EEG.filepath_original = filepath;

    % Check for consistency in EEG structure
    EEG = eeg_checkset(EEG);

    fprintf('Successfully loaded TUH EEG data:\n');
    fprintf('  Subject: %s\n', EEG.subject);
    fprintf('  Session: %d\n', EEG.session);
    fprintf('  Run: %d\n', EEG.run);
    fprintf('  Channels: %d\n', EEG.nbchan);
    fprintf('  Samples: %d\n', EEG.pnts);
    fprintf('  Sampling rate: %.2f Hz\n', EEG.srate);
    fprintf('  Duration: %.2f seconds\n', EEG.pnts/EEG.srate);

catch ME
    error('Failed to load TUH EEG file: %s\nError: %s', filepath, ME.message);
end

end

function EEG = create_eeglab_structure(data, header)
% Helper function to create EEGLAB structure from raw data and header
    EEG = eeg_emptyset();
    EEG.data = data;
    EEG.nbchan = size(data, 1);
    EEG.pnts = size(data, 2);
    EEG.srate = header.srate;
    EEG.xmin = 0;
    EEG.xmax = (EEG.pnts - 1) / EEG.srate;
    EEG.times = (0:EEG.pnts-1) / EEG.srate;

    % Set channel information
    for i = 1:EEG.nbchan
        EEG.chanlocs(i).labels = header.labels{i};
        EEG.chanlocs(i).type = 'EEG';
    end
end
