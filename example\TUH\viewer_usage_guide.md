# EEG Multichannel Viewer Usage Guide

## Overview
Two powerful MATLAB functions for visualizing multichannel EEG data with event annotations:

1. **`eeg_multichannel_viewer.m`** - Interactive viewer with keyboard controls
2. **`eeg_simple_plot.m`** - Static plotter with save functionality

## Quick Start

### Basic Usage
```matlab
% Load your EEG data
EEG_BIDS = load_bids_eeg('sub-000_ses-001_task-szMonitoring_run-01_eeg.edf');

% Interactive viewer
eeg_multichannel_viewer(EEG_BIDS);

% Static plot
eeg_simple_plot(EEG_BIDS);
```

### Focus on Seizure Events
```matlab
% View seizure event (starts at ~37 seconds)
eeg_multichannel_viewer(EEG_BIDS, 'StartTime', 30, 'TimeWindow', 20);

% Create publication-ready plot
eeg_simple_plot(EEG_BIDS, 'StartTime', 30, 'TimeWindow', 60, ...
               'SaveFigure', true, 'FigureName', 'seizure_analysis.png');
```

## Interactive Viewer Controls

### Keyboard Shortcuts
- **← →** : Navigate through time (move by half window)
- **+ -** : Zoom in/out (change time window)
- **↑ ↓** : Scale amplitude up/down
- **r** : Reset to default view
- **f** : Toggle filtering (1-50 Hz bandpass)
- **g** : Toggle grid display
- **e** : Toggle event markers

### Navigation Tips
- Use arrow keys to quickly scan through the entire recording
- Use +/- to focus on specific time periods
- Use ↑/↓ to adjust signal amplitude for better visibility

## Function Parameters

### Common Parameters (Both Functions)
```matlab
'TimeWindow'    - Time window in seconds (default: 10 for interactive, 30 for static)
'StartTime'     - Start time in seconds (default: 0)
'AmplitudeScale'- Amplitude scaling factor (default: 1)
'ShowEvents'    - Show event markers (default: true)
'FilterData'    - Apply 1-50 Hz bandpass filter (default: false)
```

### Interactive Viewer Specific
```matlab
'ChannelSpacing'- Vertical spacing between channels (default: auto)
'ShowGrid'      - Show time grid (default: true)
'Title'         - Custom plot title (default: auto-generated)
```

### Static Plot Specific
```matlab
'SaveFigure'    - Save figure to file (default: false)
'FigureName'    - Output filename (default: 'eeg_plot.png')
```

## Example Use Cases

### 1. Clinical Review
```matlab
% Load patient data
EEG = load_bids_eeg('patient_data.edf');

% Interactive review with filtering
eeg_multichannel_viewer(EEG, 'FilterData', true, 'TimeWindow', 15);
```

### 2. Seizure Analysis
```matlab
% Focus on seizure events
eeg_multichannel_viewer(EEG, 'StartTime', 35, 'TimeWindow', 25, 'AmplitudeScale', 2);

% Create publication figure
eeg_simple_plot(EEG, 'StartTime', 30, 'TimeWindow', 40, ...
               'FilterData', true, 'SaveFigure', true, ...
               'FigureName', 'seizure_onset_analysis.png');
```

### 3. Data Quality Assessment
```matlab
% Quick overview of entire recording
eeg_simple_plot(EEG, 'TimeWindow', 300, 'AmplitudeScale', 0.5);

% Detailed inspection with filtering
eeg_multichannel_viewer(EEG, 'FilterData', true, 'ShowGrid', true);
```

### 4. Batch Processing
```matlab
% Process multiple files
files = {'patient1.edf', 'patient2.edf', 'patient3.edf'};

for i = 1:length(files)
    EEG = load_bids_eeg(files{i});
    
    % Create standardized plots
    eeg_simple_plot(EEG, 'TimeWindow', 60, 'FilterData', true, ...
                   'SaveFigure', true, ...
                   'FigureName', sprintf('patient_%d_overview.png', i));
end
```

## Event Visualization

### Event Types and Colors
- **Seizure events** (`sz_*`, `seizure`): Red solid lines
- **Background events** (`bckg`, `normal`): Green dashed lines
- **Other events**: Green dashed lines

### Event Information
- Vertical lines mark event onset times
- Event labels are rotated 90° for readability
- Event counts are displayed in info boxes

## Output Files

### Static Plot Outputs
When `SaveFigure` is true, two files are created:
1. **Standard resolution**: `filename.png` (screen resolution)
2. **High resolution**: `filename_highres.png` (300 DPI for publications)

## Tips for Best Results

### 1. Time Window Selection
- **Short windows (5-15s)**: Detailed waveform analysis
- **Medium windows (30-60s)**: Event context analysis
- **Long windows (120s+)**: Overview and pattern recognition

### 2. Amplitude Scaling
- Start with default scale (1.0)
- Increase for small amplitude signals
- Decrease for large amplitude or noisy signals

### 3. Filtering
- Use filtering for cleaner visualization
- Disable for raw data inspection
- 1-50 Hz bandpass removes most artifacts

### 4. Channel Selection
- BIDS format (19 channels): Standard clinical montage
- TUH format (33 channels): Includes reference and auxiliary channels

## Troubleshooting

### Common Issues
1. **"Invalid EEG structure"**: Ensure data is loaded with `load_bids_eeg()` or `load_tuh_eeg()`
2. **No events displayed**: Check if events exist with `isfield(EEG, 'event')`
3. **Time window too large**: Function automatically adjusts to data length
4. **Amplitude too small/large**: Use ↑/↓ keys or adjust `AmplitudeScale` parameter

### Performance Tips
- For large datasets, use shorter time windows
- Apply filtering to reduce noise and improve visibility
- Use static plot for final figures, interactive viewer for exploration

## Integration with EEGLAB

Both functions work seamlessly with EEGLAB structures:
```matlab
% Standard EEGLAB workflow
EEG = pop_loadset('dataset.set');
eeg_multichannel_viewer(EEG);

% Or with our custom loaders
EEG = load_bids_eeg('data.edf');
eeg_simple_plot(EEG, 'SaveFigure', true);
```

## Advanced Features

### Custom Event Highlighting
Events are automatically color-coded based on type:
- Seizure-related events appear in red
- Background/normal events appear in green
- Custom event types can be added by modifying the color logic

### Multi-Dataset Comparison
```matlab
% Load multiple datasets
EEG1 = load_bids_eeg('pre_treatment.edf');
EEG2 = load_bids_eeg('post_treatment.edf');

% Create comparison plots
eeg_simple_plot(EEG1, 'SaveFigure', true, 'FigureName', 'pre_treatment.png');
eeg_simple_plot(EEG2, 'SaveFigure', true, 'FigureName', 'post_treatment.png');
```
