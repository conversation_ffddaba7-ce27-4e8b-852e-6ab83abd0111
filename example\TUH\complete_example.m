%% Complete EEG Analysis Example
% This script demonstrates the complete workflow for loading and visualizing
% TUH and BIDS EEG data with seizure events

clear; clc; close all;

%% 1. Load BIDS EEG Data (recommended for analysis)
fprintf('=== Loading BIDS EEG Data ===\n');
EEG_BIDS = load_bids_eeg('sub-000_ses-001_task-szMonitoring_run-01_eeg.edf');

%% 2. Interactive Viewer - Explore the data
fprintf('\n=== Interactive Viewer ===\n');
fprintf('Opening interactive viewer...\n');
fprintf('Use keyboard controls to navigate:\n');
fprintf('  ← → : Navigate time\n');
fprintf('  + - : Zoom in/out\n');
fprintf('  ↑ ↓ : Scale amplitude\n');
fprintf('  r   : Reset view\n');
fprintf('  f   : Toggle filter\n');

% Start with overview
eeg_multichannel_viewer(EEG_BIDS, 'TimeWindow', 30, 'StartTime', 0);

%% 3. Focus on Seizure Event
fprintf('\n=== Seizure Event Analysis ===\n');
fprintf('Focusing on seizure event at ~37 seconds...\n');

% Interactive view of seizure
eeg_multichannel_viewer(EEG_BIDS, ...
                       'StartTime', 25, ...        % Start before seizure
                       'TimeWindow', 30, ...       % 30-second window
                       'AmplitudeScale', 2, ...    % Increase amplitude
                       'ShowEvents', true, ...     % Show events
                       'FilterData', false, ...    % No filtering initially
                       'Title', 'Seizure Event Analysis');

%% 4. Create Publication-Quality Plots
fprintf('\n=== Creating Publication Plots ===\n');

% Plot 1: Overview of entire recording
eeg_simple_plot(EEG_BIDS, ...
               'TimeWindow', 301, ...              % Entire recording
               'StartTime', 0, ...
               'AmplitudeScale', 0.8, ...
               'ShowEvents', true, ...
               'SaveFigure', true, ...
               'FigureName', 'eeg_overview.png');

% Plot 2: Seizure onset and progression
eeg_simple_plot(EEG_BIDS, ...
               'TimeWindow', 60, ...               % 1-minute window
               'StartTime', 20, ...                % Start before seizure
               'AmplitudeScale', 1.5, ...
               'ShowEvents', true, ...
               'SaveFigure', true, ...
               'FigureName', 'seizure_onset.png');

% Plot 3: Filtered view for cleaner visualization
eeg_simple_plot(EEG_BIDS, ...
               'TimeWindow', 40, ...
               'StartTime', 30, ...
               'AmplitudeScale', 2, ...
               'FilterData', true, ...             % Apply 1-50 Hz filter
               'ShowEvents', true, ...
               'SaveFigure', true, ...
               'FigureName', 'seizure_filtered.png');

%% 5. Event Analysis
fprintf('\n=== Event Analysis ===\n');
if isfield(EEG_BIDS, 'event') && ~isempty(EEG_BIDS.event)
    fprintf('Event Summary:\n');
    event_types = {EEG_BIDS.event.type};
    unique_types = unique(event_types);
    
    for i = 1:length(unique_types)
        count = sum(strcmp(event_types, unique_types{i}));
        fprintf('  %s: %d events\n', unique_types{i}, count);
    end
    
    fprintf('\nDetailed Event List:\n');
    for i = 1:length(EEG_BIDS.event)
        event_time = (EEG_BIDS.event(i).latency - 1) / EEG_BIDS.srate;
        duration = EEG_BIDS.event(i).duration / EEG_BIDS.srate;
        fprintf('  Event %d: %s at %.2f s (duration: %.2f s)\n', ...
                i, EEG_BIDS.event(i).type, event_time, duration);
    end
end

%% 6. Compare with TUH Format (Optional)
fprintf('\n=== TUH vs BIDS Comparison ===\n');
try
    % Load TUH data
    EEG_TUH = load_tuh_eeg('aaaaaaac_s001_t000.edf');
    
    % Compare formats
    comparison_report = compare_tuh_bids_consistency('aaaaaaac_s001_t000.edf', ...
                                                    'sub-000_ses-001_task-szMonitoring_run-01_eeg.edf');
    
    % Quick visualization of TUH data
    eeg_simple_plot(EEG_TUH, ...
                   'TimeWindow', 30, ...
                   'StartTime', 30, ...
                   'AmplitudeScale', 0.3, ...       % Smaller scale for 33 channels
                   'ShowEvents', false, ...         % TUH has no events
                   'SaveFigure', true, ...
                   'FigureName', 'tuh_comparison.png');
    
    fprintf('Comparison completed. Check comparison_report for details.\n');
    
catch ME
    fprintf('Could not load TUH data: %s\n', ME.message);
end

%% 7. Summary
fprintf('\n=== Analysis Summary ===\n');
fprintf('Dataset: %s\n', EEG_BIDS.setname);
fprintf('Subject: %s\n', EEG_BIDS.subject);
fprintf('Task: %s\n', EEG_BIDS.task);
fprintf('Channels: %d\n', EEG_BIDS.nbchan);
fprintf('Sampling Rate: %.1f Hz\n', EEG_BIDS.srate);
fprintf('Duration: %.1f seconds\n', EEG_BIDS.pnts/EEG_BIDS.srate);

if isfield(EEG_BIDS, 'event') && ~isempty(EEG_BIDS.event)
    fprintf('Total Events: %d\n', length(EEG_BIDS.event));
    
    % Find seizure events
    seizure_events = 0;
    for i = 1:length(EEG_BIDS.event)
        if contains(lower(EEG_BIDS.event(i).type), 'sz')
            seizure_events = seizure_events + 1;
        end
    end
    fprintf('Seizure Events: %d\n', seizure_events);
end

fprintf('\nGenerated Files:\n');
fprintf('  - eeg_overview.png (full recording overview)\n');
fprintf('  - seizure_onset.png (seizure event focus)\n');
fprintf('  - seizure_filtered.png (filtered view)\n');
if exist('EEG_TUH', 'var')
    fprintf('  - tuh_comparison.png (TUH format comparison)\n');
end

fprintf('\n=== Analysis Complete! ===\n');
fprintf('Use the interactive viewer to explore specific time periods:\n');
fprintf('  eeg_multichannel_viewer(EEG_BIDS, ''StartTime'', 35, ''TimeWindow'', 20);\n');

%% 8. Advanced Analysis Examples
fprintf('\n=== Advanced Analysis Examples ===\n');

% Example: Extract seizure data for further analysis
if isfield(EEG_BIDS, 'event') && ~isempty(EEG_BIDS.event)
    % Find seizure event
    seizure_idx = find(contains({EEG_BIDS.event.type}, 'sz'));
    
    if ~isempty(seizure_idx)
        seizure_start = (EEG_BIDS.event(seizure_idx(1)).latency - 1) / EEG_BIDS.srate;
        seizure_duration = EEG_BIDS.event(seizure_idx(1)).duration / EEG_BIDS.srate;
        
        fprintf('Seizure Analysis:\n');
        fprintf('  Onset: %.2f seconds\n', seizure_start);
        fprintf('  Duration: %.2f seconds\n', seizure_duration);
        
        % Extract seizure data (with 10-second buffer)
        buffer = 10; % seconds
        start_sample = max(1, round((seizure_start - buffer) * EEG_BIDS.srate));
        end_sample = min(EEG_BIDS.pnts, round((seizure_start + seizure_duration + buffer) * EEG_BIDS.srate));
        
        seizure_data = EEG_BIDS.data(:, start_sample:end_sample);
        seizure_time = (start_sample:end_sample) / EEG_BIDS.srate;
        
        fprintf('  Extracted data: %d channels x %d samples\n', size(seizure_data));
        fprintf('  Time range: %.2f - %.2f seconds\n', seizure_time(1), seizure_time(end));
        
        % Save seizure data for further analysis
        save('seizure_data.mat', 'seizure_data', 'seizure_time', 'seizure_start', 'seizure_duration');
        fprintf('  Saved to: seizure_data.mat\n');
    end
end

fprintf('\nExample completed! All functions are ready for your analysis.\n');
