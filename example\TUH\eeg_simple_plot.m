function eeg_simple_plot(EEG, varargin)
% EEG_SIMPLE_PLOT Simple multichannel EEG data plotter with events
%
% Usage:
%   eeg_simple_plot(EEG)
%   eeg_simple_plot(EEG, 'Parameter', Value, ...)
%
% Inputs:
%   EEG - EEGLAB structure containing EEG data
%
% Optional Parameters:
%   'TimeWindow'    - Time window to display in seconds (default: 30)
%   'StartTime'     - Start time in seconds (default: 0)
%   'AmplitudeScale'- Amplitude scaling factor (default: 1)
%   'ShowEvents'    - Show event markers (default: true)
%   'FilterData'    - Apply basic filtering (default: false)
%   'SaveFigure'    - Save figure to file (default: false)
%   'FigureName'    - Figure filename if saving (default: 'eeg_plot.png')
%
% Example:
%   eeg_simple_plot(EEG_BIDS, 'TimeWindow', 20, 'StartTime', 30);

% Parse input arguments
p = inputParser;
addRequired(p, 'EEG', @isstruct);
addParameter(p, 'TimeWindow', 30, @(x) isnumeric(x) && x > 0);
addParameter(p, 'StartTime', 0, @(x) isnumeric(x) && x >= 0);
addParameter(p, 'AmplitudeScale', 1, @(x) isnumeric(x) && x > 0);
addParameter(p, 'ShowEvents', true, @islogical);
addParameter(p, 'FilterData', false, @islogical);
addParameter(p, 'SaveFigure', false, @islogical);
addParameter(p, 'FigureName', 'eeg_plot.png', @ischar);

parse(p, EEG, varargin{:});

% Extract parameters
time_window = p.Results.TimeWindow;
start_time = p.Results.StartTime;
amplitude_scale = p.Results.AmplitudeScale;
show_events = p.Results.ShowEvents;
filter_data = p.Results.FilterData;
save_figure = p.Results.SaveFigure;
figure_name = p.Results.FigureName;

% Validate EEG structure
if ~isfield(EEG, 'data') || ~isfield(EEG, 'srate')
    error('Invalid EEG structure. Must contain data and srate fields.');
end

% Get data dimensions
[n_channels, n_samples] = size(EEG.data);
total_time = n_samples / EEG.srate;

% Validate time parameters
if start_time >= total_time
    start_time = 0;
    warning('Start time exceeds data length. Reset to 0.');
end

if start_time + time_window > total_time
    time_window = total_time - start_time;
    warning('Time window adjusted to fit data length.');
end

% Create time vector
time_vector = (0:n_samples-1) / EEG.srate;

% Get sample indices for the time window
start_sample = max(1, round(start_time * EEG.srate) + 1);
end_sample = min(n_samples, round((start_time + time_window) * EEG.srate));
sample_indices = start_sample:end_sample;
time_subset = time_vector(sample_indices);

% Extract data for the time window
data_subset = EEG.data(:, sample_indices);

% Apply filtering if requested
if filter_data
    fprintf('Applying basic bandpass filter (1-50 Hz)...\n');
    for ch = 1:n_channels
        data_subset(ch, :) = bandpass(data_subset(ch, :), [1 50], EEG.srate);
    end
end

% Calculate channel spacing
data_range = max(data_subset(:)) - min(data_subset(:));
channel_spacing = data_range / n_channels * 1.5;

% Apply amplitude scaling
data_subset = data_subset * amplitude_scale;

% Create figure
fig = figure('Name', 'EEG Multichannel Plot', 'NumberTitle', 'off', ...
             'Position', [100, 100, 1400, 900]);

% Create main axes
ax = axes('Position', [0.08, 0.1, 0.85, 0.8]);
hold on;

% Plot each channel with vertical offset
channel_offsets = (0:n_channels-1) * channel_spacing;
colors = lines(n_channels);

for ch = 1:n_channels
    y_data = data_subset(ch, :) + channel_offsets(ch);
    plot(time_subset, y_data, 'Color', colors(ch, :), 'LineWidth', 1);
end

% Set axis properties
xlim([time_subset(1), time_subset(end)]);
ylim([-channel_spacing, (n_channels-1)*channel_spacing + channel_spacing]);

% Add channel labels
if isfield(EEG, 'chanlocs') && ~isempty(EEG.chanlocs)
    channel_labels = {EEG.chanlocs.labels};
else
    channel_labels = arrayfun(@(x) sprintf('Ch%d', x), 1:n_channels, 'UniformOutput', false);
end

% Add y-axis labels for channels
yticks(channel_offsets);
yticklabels(channel_labels);
ylabel('Channels', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('Time (seconds)', 'FontSize', 12, 'FontWeight', 'bold');

% Add grid
grid on;
set(ax, 'GridAlpha', 0.3);

% Add events if available and requested
if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    fprintf('Adding %d events to plot...\n', length(EEG.event));
    
    for i = 1:length(EEG.event)
        event_time = (EEG.event(i).latency - 1) / EEG.srate;
        
        % Only show events within the current time window
        if event_time >= time_subset(1) && event_time <= time_subset(end)
            event_type = EEG.event(i).type;
            
            % Color code events
            if contains(lower(event_type), 'sz') || contains(lower(event_type), 'seizure')
                event_color = [1, 0, 0]; % Red for seizures
                line_width = 3;
                line_style = '-';
            else
                event_color = [0, 0.7, 0]; % Green for other events
                line_width = 2;
                line_style = '--';
            end
            
            % Draw vertical line for event
            line([event_time, event_time], ylim, ...
                 'Color', event_color, 'LineWidth', line_width, ...
                 'LineStyle', line_style);
            
            % Add event label
            text(event_time, max(ylim) * 0.95, event_type, ...
                 'Rotation', 90, 'FontSize', 10, 'Color', event_color, ...
                 'HorizontalAlignment', 'right', 'VerticalAlignment', 'top', ...
                 'FontWeight', 'bold');
        end
    end
end

% Set title
if isfield(EEG, 'setname')
    plot_title = EEG.setname;
else
    plot_title = sprintf('EEG Data (%d channels, %.1f Hz)', n_channels, EEG.srate);
end

title_text = sprintf('%s\nTime: %.1f - %.1f s (Duration: %.1f s)', ...
                    plot_title, time_subset(1), time_subset(end), time_window);

if filter_data
    title_text = [title_text, ' - Filtered (1-50 Hz)'];
end

title(title_text, 'Interpreter', 'none', 'FontSize', 14, 'FontWeight', 'bold');

% Add information box
info_text = sprintf(['Dataset Info:\n' ...
                    'Channels: %d\n' ...
                    'Sampling Rate: %.1f Hz\n' ...
                    'Total Duration: %.1f s\n' ...
                    'Amplitude Scale: %.1fx'], ...
                    n_channels, EEG.srate, total_time, amplitude_scale);

if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    info_text = [info_text, sprintf('\nEvents: %d loaded', length(EEG.event))];
end

annotation('textbox', [0.92, 0.7, 0.07, 0.25], 'String', info_text, ...
           'FontSize', 9, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
           'VerticalAlignment', 'top');

% Add event legend if events are shown
if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    % Count event types
    event_types = {EEG.event.type};
    unique_types = unique(event_types);
    
    legend_text = 'Event Types:\n';
    for i = 1:length(unique_types)
        count = sum(strcmp(event_types, unique_types{i}));
        if contains(lower(unique_types{i}), 'sz') || contains(lower(unique_types{i}), 'seizure')
            color_desc = 'Red';
        else
            color_desc = 'Green';
        end
        legend_text = [legend_text, sprintf('%s (%s): %d\n', unique_types{i}, color_desc, count)];
    end
    
    annotation('textbox', [0.92, 0.4, 0.07, 0.25], 'String', legend_text, ...
               'FontSize', 9, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
               'VerticalAlignment', 'top');
end

% Improve appearance
set(ax, 'FontSize', 10);
set(ax, 'LineWidth', 1);

% Save figure if requested
if save_figure
    fprintf('Saving figure to: %s\n', figure_name);
    saveas(fig, figure_name);
    
    % Also save as high-resolution PNG
    [~, name, ~] = fileparts(figure_name);
    high_res_name = [name, '_highres.png'];
    print(fig, high_res_name, '-dpng', '-r300');
    fprintf('High-resolution version saved to: %s\n', high_res_name);
end

fprintf('EEG plot created successfully!\n');
fprintf('Dataset: %s\n', plot_title);
fprintf('Time window: %.1f - %.1f seconds\n', time_subset(1), time_subset(end));
fprintf('Channels displayed: %d\n', n_channels);

if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    events_in_window = 0;
    for i = 1:length(EEG.event)
        event_time = (EEG.event(i).latency - 1) / EEG.srate;
        if event_time >= time_subset(1) && event_time <= time_subset(end)
            events_in_window = events_in_window + 1;
        end
    end
    fprintf('Events in current window: %d\n', events_in_window);
end

end
