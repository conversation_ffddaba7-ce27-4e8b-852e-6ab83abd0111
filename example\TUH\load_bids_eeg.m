function EEG = load_bids_eeg(filepath, eeglab_path)
% LOAD_BIDS_EEG Load BIDS format EEG data and return EEGLAB structure
%
% Usage:
%   EEG = load_bids_eeg(filepath, eeglab_path)
%
% Inputs:
%   filepath    - Full path to the BIDS .edf file (e.g., 'sub-000_ses-001_task-szMonitoring_run-01_eeg.edf')
%   eeglab_path - Path to EEGLAB installation (optional, default: 'E:\code\tools\eeglab2025.0.0\')
%
% Outputs:
%   EEG         - EEGLAB structure containing the loaded EEG data with events
%
% Example:
%   EEG = load_bids_eeg('sub-000_ses-001_task-szMonitoring_run-01_eeg.edf');
%   EEG = load_bids_eeg('sub-000_ses-001_task-szMonitoring_run-01_eeg.edf', 'E:\code\tools\eeglab2025.0.0\');

% Set default EEGLAB path if not provided
if nargin < 2 || isempty(eeglab_path)
    eeglab_path = 'E:\code\tools\eeglab2025.0.0\';
end

% Add EEGLAB to path if not already added
if ~exist('eeglab', 'file')
    addpath(genpath(eeglab_path));
end

% Initialize EEGLAB (suppress GUI)
if ~exist('ALLEEG', 'var')
    eeglab('nogui');
end

% Check if file exists
if ~exist(filepath, 'file')
    error('File not found: %s', filepath);
end

try
    % Load EDF file using EEGLAB's pop_biosig function or alternative methods
    fprintf('Loading BIDS EEG file: %s\n', filepath);

    % Try different methods to load EDF file
    biosig_available = false;
    try
        % Test if pop_biosig works
        if exist('pop_biosig', 'file')
            % Try a quick test to see if biosig extension is actually available
            test_result = which('pop_biosig');
            if ~isempty(test_result)
                biosig_available = true;
            end
        end
    catch
        biosig_available = false;
    end

    if biosig_available
        try
            % Method 1: Use pop_biosig (requires Biosig extension)
            EEG = pop_biosig(filepath);
        catch ME
            if contains(ME.message, 'Biosig')
                fprintf('Biosig extension not available, using basic EDF reader...\n');
                biosig_available = false;
            else
                rethrow(ME);
            end
        end
    end

    if ~biosig_available
        if exist('pop_readedf', 'file')
            % Method 2: Use pop_readedf (built-in EEGLAB function)
            try
                EEG = pop_readedf(filepath);
            catch
                % If pop_readedf fails, use basic reader
                fprintf('pop_readedf failed, using basic EDF reader...\n');
                [data, header] = read_edf_basic(filepath);
                EEG = create_eeglab_structure(data, header);
            end
        else
            % Method 3: Use basic EDF reader and create EEGLAB structure manually
            fprintf('Using basic EDF reader...\n');
            [data, header] = read_edf_basic(filepath);
            EEG = create_eeglab_structure(data, header);
        end
    end

    % Extract information from BIDS filename
    [filepath_dir, filename, ~] = fileparts(filepath);

    % Parse BIDS filename format: sub-000_ses-001_task-szMonitoring_run-01_eeg
    parts = split(filename, '_');

    % Initialize BIDS fields
    subject_id = '';
    session_num = 1;
    task_name = '';
    run_num = 1;

    % Parse each part of the BIDS filename
    for i = 1:length(parts)
        part = parts{i};
        if startsWith(part, 'sub-')
            subject_id = part(5:end); % Remove 'sub-' prefix
        elseif startsWith(part, 'ses-')
            session_num = str2double(part(5:end)); % Remove 'ses-' prefix
        elseif startsWith(part, 'task-')
            task_name = part(6:end); % Remove 'task-' prefix
        elseif startsWith(part, 'run-')
            run_num = str2double(part(5:end)); % Remove 'run-' prefix
        end
    end

    % Set EEGLAB structure fields
    EEG.subject = subject_id;
    EEG.session = session_num;
    EEG.run = run_num;
    EEG.task = task_name;

    % Set dataset name
    EEG.setname = sprintf('BIDS_sub-%s_ses-%03d_task-%s_run-%02d', ...
                         subject_id, session_num, task_name, run_num);

    % Add metadata
    EEG.format = 'BIDS';
    EEG.filepath_original = filepath;

    % Look for corresponding events file
    events_file = fullfile(filepath_dir, strrep(filename, '_eeg', '_events.tsv'));

    if exist(events_file, 'file')
        fprintf('Loading events from: %s\n', events_file);

        % Read events TSV file
        events_data = readtable(events_file, 'FileType', 'text', 'Delimiter', '\t');

        % Convert events to EEGLAB format
        if height(events_data) > 0
            % Get unique events (remove channel-specific duplicates)
            [unique_onsets, unique_idx] = unique(events_data.onset);
            unique_events = events_data(unique_idx, :);

            % Create EEGLAB events structure
            EEG.event = [];
            for i = 1:height(unique_events)
                EEG.event(i).type = unique_events.eventType{i};
                EEG.event(i).latency = unique_events.onset(i) * EEG.srate + 1; % Convert to samples (1-indexed)
                EEG.event(i).duration = unique_events.duration(i) * EEG.srate; % Convert to samples

                % Add additional fields if available
                if ismember('confidence', unique_events.Properties.VariableNames)
                    EEG.event(i).confidence = unique_events.confidence(i);
                end
                if ismember('dateTime', unique_events.Properties.VariableNames)
                    % Handle dateTime field safely
                    if iscell(unique_events.dateTime)
                        EEG.event(i).dateTime = unique_events.dateTime{i};
                    else
                        EEG.event(i).dateTime = unique_events.dateTime(i);
                    end
                end
            end

            fprintf('Loaded %d events\n', length(EEG.event));

            % Display event summary
            event_types = {EEG.event.type};
            unique_types = unique(event_types);
            for j = 1:length(unique_types)
                count = sum(strcmp(event_types, unique_types{j}));
                fprintf('  %s: %d events\n', unique_types{j}, count);
            end
        end
    else
        fprintf('No events file found: %s\n', events_file);
    end

    % Check for consistency in EEG structure
    EEG = eeg_checkset(EEG);

    fprintf('Successfully loaded BIDS EEG data:\n');
    fprintf('  Subject: %s\n', EEG.subject);
    fprintf('  Session: %d\n', EEG.session);
    fprintf('  Task: %s\n', EEG.task);
    fprintf('  Run: %d\n', EEG.run);
    fprintf('  Channels: %d\n', EEG.nbchan);
    fprintf('  Samples: %d\n', EEG.pnts);
    fprintf('  Sampling rate: %.2f Hz\n', EEG.srate);
    fprintf('  Duration: %.2f seconds\n', EEG.pnts/EEG.srate);

catch ME
    error('Failed to load BIDS EEG file: %s\nError: %s', filepath, ME.message);
end

end

function EEG = create_eeglab_structure(data, header)
% Helper function to create EEGLAB structure from raw data and header
    EEG = eeg_emptyset();
    EEG.data = data;
    EEG.nbchan = size(data, 1);
    EEG.pnts = size(data, 2);
    EEG.srate = header.srate;
    EEG.xmin = 0;
    EEG.xmax = (EEG.pnts - 1) / EEG.srate;
    EEG.times = (0:EEG.pnts-1) / EEG.srate;

    % Set channel information
    for i = 1:EEG.nbchan
        EEG.chanlocs(i).labels = header.labels{i};
        EEG.chanlocs(i).type = 'EEG';
    end
end