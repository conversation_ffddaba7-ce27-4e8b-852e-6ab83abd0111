close all

% %{

N=12000;%12000
type_attractor='criticality';%point limit_cycle chaos
[x_sim, Fs] = gen_nf_ts([], [], N);%gen_lorenz_ts

%%
k=2;
opt.use_threshold=false;
opt.p=5;
[cnars] = fit_cnar_unsup(x_sim,k,opt);
cnars.Fs=200;

%%
figure(101);clf
visualize_cnars_train(cnars)



%%
% Estimate potential landscape
sigma=0;
cnars.opt.issoft=false;
result = estimate_potential_cnar(cnars, 200, 100, sigma, 500);

% %%
figure(102); clf;
% Visualize with optional trajectory
plot_potential_pca(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off

% %%
figure(103); clf;
% Visualize with optional trajectory
plot_potential_cnar(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off

%%
cnars.opt.vis_y=true;
[cnars] = gen_cluster_lpr_ts_all_random(cnars, 'gaussian', N,true);
