function plot_potential_cnar(result, low_dim_traj)
% PLOT_POTENTIAL_PCA - Visualizes potential landscape with cluster probabilities
% Inputs:
%   result        - Struct containing GMM params and potential data
%   low_dim_traj  - Optional trajectory coordinates (N x 2 matrix)

% Extract main components
X1 = result.X1;
X2 = result.X2;
U = result.U;
k = size(result.mu_low, 1);

% Create figure with improved layout
fig = figure('Color', 'w', 'Position', [100 100 1400 900]);
ax = axes('Parent', fig, 'Position', [0.1 0.1 0.8 0.8]);
hold(ax, 'on');

% ================== Potential Landscape Base ==================
surf(X1, X2, U, 'EdgeColor', 'none');
shading interp
colormap(redbluecmap)
clim([min(U(:)) max(U(:))])
view(-37.5, 30)
% ================== Cluster Probability Meshes ==================
% Compute posterior probabilities for each cluster
[posterior_probs, colors] = compute_cluster_probabilities(result, X1, X2);

% Plot probability meshes with depth sorting
prob_offset = 0.15 * range(U(:)); % Vertical offset for probability meshes
for c = k:-1:1 % Reverse order for proper depth layering
    alpha_data = 0.3*posterior_probs(:,:,c);
    alpha_data(alpha_data < 0.1) = 0; % Threshold for cleaner visualization
    
    % % Create surface for each cluster with only edge color
    % surf(X1, X2, U + prob_offset, ...
    %     'FaceColor', 'none', ...
    %     'EdgeColor', colors(c,:), ...
    %     'EdgeAlpha', 0.3, ...
    %     'Tag', ['Cluster', num2str(c)]);
    prob_surf(c) = surf(X1, X2, U + prob_offset,...
        'FaceColor', colors(c,:),...
        'FaceAlpha', 'texture',...
        'AlphaData', alpha_data,...
        'EdgeColor', 'none',...
        'FaceLighting', 'none',...
        'Tag', ['Cluster', num2str(c)]);
end

% ================== Enhanced Cluster Boundaries ==================
% boundary_levels = linspace(0.5, max(result.cluster_grid(:))-0.5, k-1);
% for bl = boundary_levels
%     [~, h] = contour3(X1, X2, result.cluster_grid, [bl bl],...
%         'LineWidth', 2.5,...
%         'Color', [0 0 0]);
% end

% ================== Trajectory Visualization ==================
if exist('low_dim_traj', 'var') && ~isempty(low_dim_traj)
    % Process trajectory
    [traj_3d, arrow_pos] = process_trajectory(low_dim_traj, X1, X2, U, prob_offset);

    % Main trajectory line
    plot3(traj_3d(:,1), traj_3d(:,2), traj_3d(:,3),...
        'Color', [0.9 0.1 0.1 0.8], 'LineWidth', 3);

    % Directional arrows
    for a = 1:size(arrow_pos,1)
        arrow3(arrow_pos(a,1:3), arrow_pos(a,4:6),...
            'r1a2', 1.5, 0.2,...
            'EdgeAlpha', 0.8,...
            'FaceAlpha', 0.8);
    end

    % Start/end markers
    scatter3(traj_3d([1 end],1), traj_3d([1 end],2), traj_3d([1 end],3),...
        120, 'filled',...
        'MarkerFaceColor', [0.2 0.8 0.2],...
        'MarkerEdgeColor', 'k',...
        'Marker', {'o','square'});
end

% ================== Professional Styling ==================
% Axis labels and limits
xlabel('PC1', 'FontWeight', 'bold', 'FontSize', 14)
ylabel('PC2', 'FontWeight', 'bold', 'FontSize', 14)
zlabel('Potential Energy', 'FontWeight', 'bold', 'FontSize', 14)
grid on
box on

% Composite colorbar
cbar = colorbar('Location', 'eastoutside');
cbar.Label.String = 'Potential Energy (kcal/mol)';
cbar.Label.FontSize = 12;

hold(ax, 'off');
end

% Helper functions remain unchanged
function [posterior_probs, colors] = compute_cluster_probabilities(result, X1, X2)
% Compute posterior probabilities for each cluster
grid_points = [X1(:), X2(:)];
k = size(result.mu_low, 1);
posterior_probs = zeros(size(X1,1), size(X1,2), k);

% Calculate component PDFs
component_pdf = zeros(numel(X1), k);
for c = 1:k
    component_pdf(:,c) = mvnpdf(grid_points,...
        gather(result.mu_low(c,:)),...
        gather(result.Sigma_low(:,:,c)));
end

% Compute posterior probabilities
weighted_pdf = component_pdf .* result.prior;
total_pdf = sum(weighted_pdf, 2);
for c = 1:k
    posterior_probs(:,:,c) = reshape(weighted_pdf(:,c)./total_pdf, size(X1));
end

% Assign perceptually distinct colors
colors = distinguishable_colors(k, [1 1 1; 0 0 0]);
end

function [traj_3d, arrow_pos] = process_trajectory(traj, X1, X2, U, offset)
% Process trajectory data for visualization
F = griddedInterpolant(X1', X2', U');
traj_smooth = smoothdata(traj, 'gaussian', 50);

% Upsample trajectory
t_orig = 1:size(traj_smooth,1);
t_interp = linspace(1, size(traj_smooth,1), 2000);
traj_interp = [interp1(t_orig, traj_smooth(:,1), t_interp, 'pchip')',...
    interp1(t_orig, traj_smooth(:,2), t_interp, 'pchip')'];

% Calculate 3D positions
U_traj = F(traj_interp(:,1), traj_interp(:,2)) + offset;
traj_3d = [traj_interp, U_traj];

% Find arrow positions at curvature maxima
[~, arrow_idx] = findpeaks(abs(gradient(gradient(traj_3d(:,3)))),...
    'MinPeakDistance', 200,...
    'NPeaks', 5);
arrow_pos = [traj_3d(arrow_idx,:), traj_3d(arrow_idx+30,:)];
end
