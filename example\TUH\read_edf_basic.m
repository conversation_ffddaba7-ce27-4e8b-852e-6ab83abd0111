function [data, header] = read_edf_basic(filename)
% READ_EDF_BASIC Basic EDF file reader for MATLAB
%
% Usage:
%   [data, header] = read_edf_basic(filename)
%
% Inputs:
%   filename - Path to EDF file
%
% Outputs:
%   data     - EEG data matrix (channels x samples)
%   header   - Structure containing header information
%
% This is a simplified EDF reader that handles basic EDF/EDF+ files

% Open file
fid = fopen(filename, 'r', 'ieee-le');
if fid == -1
    error('Cannot open file: %s', filename);
end

try
    % Read fixed header (256 bytes)
    header = struct();
    
    % Read header fields
    header.version = fread(fid, 8, 'char=>char')';
    header.patient = fread(fid, 80, 'char=>char')';
    header.recording = fread(fid, 80, 'char=>char')';
    header.startdate = fread(fid, 8, 'char=>char')';
    header.starttime = fread(fid, 8, 'char=>char')';
    header.bytes = str2double(fread(fid, 8, 'char=>char')');
    header.reserved = fread(fid, 44, 'char=>char')';
    header.records = str2double(fread(fid, 8, 'char=>char')');
    header.duration = str2double(fread(fid, 8, 'char=>char')');
    header.channels = str2double(fread(fid, 4, 'char=>char')');
    
    % Read variable header
    ns = header.channels;
    
    % Channel labels
    labels = cell(ns, 1);
    for i = 1:ns
        labels{i} = strtrim(fread(fid, 16, 'char=>char')');
    end
    header.labels = labels;
    
    % Transducer types
    transducers = cell(ns, 1);
    for i = 1:ns
        transducers{i} = strtrim(fread(fid, 80, 'char=>char')');
    end
    header.transducers = transducers;
    
    % Physical dimensions
    physdims = cell(ns, 1);
    for i = 1:ns
        physdims{i} = strtrim(fread(fid, 8, 'char=>char')');
    end
    header.physdims = physdims;
    
    % Physical minimum
    physmin = zeros(ns, 1);
    for i = 1:ns
        physmin(i) = str2double(fread(fid, 8, 'char=>char')');
    end
    header.physmin = physmin;
    
    % Physical maximum
    physmax = zeros(ns, 1);
    for i = 1:ns
        physmax(i) = str2double(fread(fid, 8, 'char=>char')');
    end
    header.physmax = physmax;
    
    % Digital minimum
    digmin = zeros(ns, 1);
    for i = 1:ns
        digmin(i) = str2double(fread(fid, 8, 'char=>char')');
    end
    header.digmin = digmin;
    
    % Digital maximum
    digmax = zeros(ns, 1);
    for i = 1:ns
        digmax(i) = str2double(fread(fid, 8, 'char=>char')');
    end
    header.digmax = digmax;
    
    % Prefiltering
    prefilters = cell(ns, 1);
    for i = 1:ns
        prefilters{i} = strtrim(fread(fid, 80, 'char=>char')');
    end
    header.prefilters = prefilters;
    
    % Samples per record
    samples_per_record = zeros(ns, 1);
    for i = 1:ns
        samples_per_record(i) = str2double(fread(fid, 8, 'char=>char')');
    end
    header.samples_per_record = samples_per_record;
    
    % Reserved fields
    reserved2 = cell(ns, 1);
    for i = 1:ns
        reserved2{i} = fread(fid, 32, 'char=>char')';
    end
    header.reserved2 = reserved2;
    
    % Calculate sampling rate (assuming all channels have same rate)
    header.srate = samples_per_record(1) / header.duration;
    
    % Calculate scaling factors
    scale = (physmax - physmin) ./ (digmax - digmin);
    offset = physmin - scale .* digmin;
    
    % Read data
    total_samples = sum(samples_per_record);
    total_records = header.records;
    
    % Initialize data matrix
    max_samples = max(samples_per_record);
    data = zeros(ns, max_samples * total_records);
    
    % Read data record by record
    sample_idx = 1;
    for record = 1:total_records
        for ch = 1:ns
            % Read samples for this channel in this record
            raw_data = fread(fid, samples_per_record(ch), 'int16');
            
            % Apply scaling
            scaled_data = raw_data * scale(ch) + offset(ch);
            
            % Store in data matrix
            end_idx = sample_idx + samples_per_record(ch) - 1;
            if ch == 1
                current_end = end_idx;
            end
            data(ch, sample_idx:end_idx) = scaled_data;
        end
        sample_idx = current_end + 1;
    end
    
    % Trim data to actual size
    actual_samples = sample_idx - 1;
    data = data(:, 1:actual_samples);
    
    % Clean up labels (remove extra spaces and invalid characters)
    for i = 1:length(labels)
        labels{i} = regexprep(labels{i}, '[^a-zA-Z0-9_-]', '');
        if isempty(labels{i})
            labels{i} = sprintf('Ch%d', i);
        end
    end
    header.labels = labels;
    
    fprintf('EDF file loaded successfully:\n');
    fprintf('  Channels: %d\n', ns);
    fprintf('  Samples per channel: %d\n', size(data, 2));
    fprintf('  Sampling rate: %.2f Hz\n', header.srate);
    fprintf('  Duration: %.2f seconds\n', size(data, 2) / header.srate);
    
catch ME
    fclose(fid);
    rethrow(ME);
end

fclose(fid);

end
