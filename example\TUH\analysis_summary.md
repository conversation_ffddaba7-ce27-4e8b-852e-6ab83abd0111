# TUH vs BIDS EEG Data Analysis Summary

## Overview
Successfully created MATLAB functions to load and compare TUH and BIDS format EEG data using EEGLAB with BIOSIG extension.

## Functions Created
1. **`load_tuh_eeg.m`** - Loads TUH format EEG data
2. **`load_bids_eeg.m`** - Loads BIDS format EEG data with events
3. **`compare_tuh_bids_consistency.m`** - Compares consistency between formats
4. **`read_edf_basic.m`** - Basic EDF reader (fallback method)
5. **`test_eeg_loading.m`** - Test script demonstrating usage

## Dataset Comparison Results

### TUH Dataset (`aaaaaaac_s001_t000.edf`)
- **Subject:** aaaaaaac
- **Channels:** 33
- **Sampling Rate:** 250 Hz
- **Duration:** 301.00 seconds
- **Events:** None embedded
- **Reference:** Linked ears (LE)
- **Channel Types:** Standard EEG + EKG + reference + monitoring channels

### BIDS Dataset (`sub-000_ses-001_task-szMonitoring_run-01_eeg.edf`)
- **Subject:** 000
- **Task:** szMonitoring
- **Channels:** 19 (standard EEG only)
- **Sampling Rate:** 256 Hz
- **Duration:** 301.00 seconds
- **Events:** 8 events (7 background, 1 seizure focus)
- **Reference:** Average reference (Avg)
- **Channel Types:** Standard EEG channels only

## Key Differences

### 1. Channel Configuration
- **TUH (33 channels):** FP1-LE, FP2-LE, F3-LE, F4-LE, C3-LE, C4-LE, A1-LE, A2-LE, P3-LE, P4-LE, O1-LE, O2-LE, F7-LE, F8-LE, T3-LE, T4-LE, T5-LE, T6-LE, FZ-LE, CZ-LE, PZ-LE, OZ-LE, PG1-LE, PG2-LE, EKG-LE, SP2-LE, SP1-LE, RLC-LE, LUC-LE, 30-LE, T1-LE, T2-LE, PHOTIC PH
- **BIDS (19 channels):** FP1-Avg, F3-Avg, C3-Avg, P3-Avg, O1-Avg, F7-Avg, T3-Avg, T5-Avg, FZ-Avg, CZ-Avg, PZ-Avg, FP2-Avg, F4-Avg, C4-Avg, P4-Avg, O2-Avg, F8-Avg, T4-Avg, T6-Avg

### 2. Processing Differences
- **Sampling Rate:** TUH (250 Hz) vs BIDS (256 Hz) - indicates resampling
- **Reference:** TUH uses linked ears (LE), BIDS uses average reference (Avg)
- **Channel Selection:** BIDS filtered to standard 19 EEG channels, removing auxiliary channels

### 3. Event Annotations
- **TUH:** No events embedded in EDF file
- **BIDS:** Rich event annotations from TSV file:
  - `bckg`: Background periods (7 events)
  - `sz_foc_ia`: Seizure focus periods (1 event)

## Interpretation

The BIDS dataset appears to be a **processed derivative** of the original TUH data:

1. **Standardization:** Converted to standard 19-channel montage
2. **Resampling:** Upsampled from 250 Hz to 256 Hz
3. **Re-referencing:** Changed from linked ears to average reference
4. **Annotation:** Added clinical event annotations
5. **Cleaning:** Removed non-EEG channels (EKG, reference electrodes, etc.)

## Clinical Significance

### Event Analysis
- **Total Duration:** 301 seconds (~5 minutes)
- **Seizure Event:** 1 focal seizure starting at 36.89 seconds
- **Background Periods:** 7 periods of normal background activity
- **Event Coverage:** Events span the entire recording duration

### Channel Montage
- **TUH:** Raw hospital recording with full montage
- **BIDS:** Standardized research montage (10-20 system)

## Recommendations

1. **For Research:** Use BIDS format for standardized analysis
2. **For Clinical Review:** Use TUH format for complete clinical picture
3. **For Event Analysis:** BIDS format provides rich annotations
4. **For Preprocessing:** Consider the different referencing schemes when comparing

## Technical Notes

- Functions automatically detect and use BIOSIG extension when available
- Fallback methods implemented for systems without BIOSIG
- All functions return standard EEGLAB structures for compatibility
- Event timing converted to EEGLAB sample indices automatically
