function result = estimate_potential_cnar(cnars, num_steps, grid_points, sigma, M)
% ESTIMATE_POTENTIAL_CNR - Estimate potential landscape for a clustered NAR model
%
% Inputs:
%   cnars       - Clustered NAR model structure
%   num_steps   - Number of simulation steps
%   grid_points - Grid points per dimension
%   sigma       - Noise magnitude
%   M           - Number of simulation trials
%
% Outputs:
%   result      - Struct containing landscape, trajectories, and PCA info

% 1. Simulate high-dimensional data
[x_high] = simulate_cnar(cnars, num_steps, sigma, M);
% Remove simulations containing NaN values
valid_sims = ~any(isnan(x_high), [1 2]);
x_high = x_high(:,:,valid_sims);
x_high_save=x_high;
% 2. Upsample for smoother visualization
Fs_original = 200;
Fs_upsampled = 2000;%2000
x_high = reshape(resampleGridded(reshape(x_high, size(x_high,1), []), ...
    Fs_original, Fs_upsampled), [], size(x_high,2), size(x_high,3));

% 3. Build PCA basis from all training inputs across clusters
all_X = cat(1, cnars.nars{1:cnars.k});
all_X = cat(1,all_X.X);
[coeff, ~, ~, ~, explained] = pca(all_X);

% 4. Project to low-dimensional (PCA) space
x_low = project_to_low_dim(x_high, coeff);

% 5. Define uniform grid for potential estimation
[min_x, max_x, min_y, max_y] = get_grid_range(x_low);
grid_vals = {linspace(min_x, max_x, grid_points), linspace(min_y, max_y, grid_points)};

% 6. Estimate optimal bandwidth using simulated data
% X_low_simulated = x_low;
% hlist = get_hList(100, [(max(X_low_simulated, [], 'all') - min(X_low_simulated, [], 'all')) / 200, ...
%                         (max(X_low_simulated, [], 'all') - min(X_low_simulated, [], 'all')) / 1], @logspace);
% [~, gcv_low] = NwSmoothGlobalGCV(X_low_simulated, X_low_simulated, hlist, X_low_simulated);
% h = gcv_low.hmin / 2;
[model] = nw_gcv(x_low, x_low, 'global');


% 7. Compute potential landscape
[U, X1, X2] = compute_potential_from_low_dim(x_low, grid_vals, model);




% Project GMM parameters to PCA space
k = cnars.gmm.NumComponents;
mu_low = cnars.gmm.mu * coeff(:, 1:2); % Project means
Sigma_low = zeros(2, 2, k);
for c = 1:k
    Sigma_high = cnars.gmm.Sigma(:, :, c);
    Sigma_low(:, :, c) = coeff(:, 1:2)' * Sigma_high * coeff(:, 1:2); % Project covariances
end
Sigma_low = reg_spd(Sigma_low);

prior = cnars.gmm.ComponentProportion; % Cluster priors

[cluster_grid] = compute_cluster_boundaries_projected(mu_low, Sigma_low, prior, grid_vals, X1, X2);


% 8. Return all computed results
result = struct(...
    'X1', X1, 'X2', X2, 'U', U, ...
    'coeff', coeff, 'explained', explained, ...
    'grid_vals', {grid_vals}, ...
    'model', model, 'num_steps', num_steps, ...
    'sigma', sigma, 'M', M,...
    'cluster_grid', cluster_grid, ...
    'mu_low', mu_low, 'Sigma_low', Sigma_low, 'prior', prior,'x_high',x_high_save,'x_low', reshape(x_low, size(x_high,1), size(x_high,3), 2));
end
    % 'x_low', reshape(x_low, size(x_high,1), M, 2), ...

function [x_high] = simulate_cnar(cnars, num_steps, sigma, M)
% SIMULATE_CNAR - Simulate multivariate time series from CNAR model
%
% Inputs:
%   cnars       - Clustered NAR model
%   num_steps   - Number of steps to simulate
%   sigma       - Noise magnitude
%   M           - Number of parallel trials
%   initial_states - Optional (M x p) initial states
%
% Outputs:
%   x_high      - Simulated trajectories (num_steps x M x p)

nburning = 0;

% Set initial states randomly if none provided
if nargin < 5
    all_X = cat(1, cnars.nars{1:cnars.k});
    all_X = cat(1, all_X.X);

    if M > size(all_X, 1)
        idx = randi(size(all_X,1), M, 1);
    else
        idx = randperm(size(all_X,1), M);
    end
    initial_states = all_X(idx, :);
end

p = size(initial_states,2); % Assume all NAR models use the same history length
current_state = initial_states; % (M x p)
x_high = zeros(num_steps + nburning, p, M);
% if k>1
%     for c=k:-1:1
%         cnars.gmm_pdf_param{c}=calc_norm_pdf_param(gmm.mu(c,:),gmm.Sigma(:,:,c));
%     end
% end
cnars.gmm_pdf_param=arrayfun(@(c) calc_norm_pdf_param(cnars.gmm.mu(c,:),cnars.gmm.Sigma(:,:,c)), 1:gather(cnars.gmm.NumComponents), 'UniformOutput', false);
% mu_list = cellfun(@(x)x.Mu, cnars.gmm_pdf_param, 'UniformOutput', false);

% Loop over simulation steps
for t = 1:(num_steps + nburning)
    % 1. Compute cluster probabilities using GMM
    % log_probs = compute_cluster_log_probs(cnars, mu_list, current_state);
    % max_log = max(nllh_x_given_c, [], 2);
    % exp_probs = exp(nllh_x_given_c - repmat(max_log, 1, cnars.k));
    % cluster_probs = exp_probs ./ sum(exp_probs, 2);
    if length(cnars.nars) > 1

        nllh_x_given_c = mvnnllh_inline(cnars.gmm_pdf_param, current_state);
        nllh_xy_given_c = nllh_x_given_c;

        % NLLH for p_c_given_xy (posterior probabilities)
        nllh_xyc = nllh_xy_given_c - log(cnars.p_c);

        % Log-Sum-Exp trick to avoid numerical overflow % nllh_sum = log(sum(exp(nllh_p_xyc), 2) + 1e-8);
        max_nllh_p_xyc = max(nllh_xyc, [], 2);  % Find the max value in each row
        nllh_sum = log(sum(exp(nllh_xyc - max_nllh_p_xyc), 2)) + max_nllh_p_xyc;
        nllh_c_given_xy = nllh_xyc - nllh_sum;

        % Cluster assignment based on NLLH (minimize NLLH)
        nllh_c_given_xy = mean(nllh_c_given_xy, 3);  % Average across any third dimension
        [~, cluster_assign] = min(nllh_c_given_xy, [], 2);
        % yhat=yhat_c(:,cluster_assign);
        p_c_given_xy=exp(-nllh_c_given_xy);
        % p_c_given_xy=p_c_given_xy./sum(p_c_given_xy,2);
        if any(isinf(p_c_given_xy))
            p_c_given_xy=zeros(size(p_c_given_xy));
            p_c_given_xy(cluster_assign)=1;
        else
            p_c_given_xy=p_c_given_xy./sum(p_c_given_xy,2);
        end
    else
        cluster_assign=1;
        p_c_given_xy=1;
    end



    % 2. Sample clusters (one per trial)
    % cluster_assignments = sample_clusters(cluster_probs);

    % 3. Predict and generate noise based on selected cluster
    % y_next = zeros(M, p);
    % for m = 1:M
        % c = cluster_assignments(m);
        % nar_c = cnars.nars{c};
        % if isfield(nar_c, 'kmin')
        %     pred = NwSmoothMassCenterBatch(nar_c.X, nar_c.Y, nar_c.kmin, current_state(m,:), nar_c.h_i);
        % else
        %     pred = NwSmoothLogGaussianBatch(nar_c.X, nar_c.Y, nar_c.h, current_state(m,:));
        % end
        % [pred] = eval_nars(cnars.nars, current_state, p_c_given_xy);

        % y_next(m,:) = pred + sigma * randn(1,p) .* cnars.sigma_y_c(:,c)';
    % end
    if ~cnars.opt.issoft
        p_c_given_xy=hardmax(p_c_given_xy);
    end

    [y_next] = eval_nars(cnars.nars, current_state, p_c_given_xy);
    % noise = (nar.residual_chol * randn(nar.p, 1))';
    warning('need set the 1:end-1 to zero')
    noise = (permute(p_c_given_xy,[1,3,2]).*cnars.sigma_y_c .* randn(M,cnars.d, cnars.k));
    y_next = y_next + sum(sigma .* noise,3);

    % 4. Update state
    x_high(t,:,:) = y_next';
    current_state = y_next;
end

x_high = x_high(nburning+1:end, :, :);
end

function x_low = project_to_low_dim(x_high, coeff)
% PROJECT_TO_LOW_DIM - Projects simulations to PCA space
% Inputs:
%   x_high - High-dim data (num_steps x d x M )
%   coeff  - PCA coefficients from reduced model
% Output:
%   x_low  - Low-dim projections (N x 2)

% Reshape and project
x_flat = reshape(permute(x_high,[1,3,2]), [], size(x_high,2)); % N x 1
x_low = x_flat * coeff(:,1:2);   % N x 2

% Optional: Add temporal context by including lagged values
% x_low = [x_low(2:end,:), x_low(1:end-1,:)];
end
function [U, X1, X2] = compute_potential_from_low_dim(x_low, grid_vals,model)
% COMPUTE_POTENTIAL_FROM_LOW_DIM - Calculates landscape from projections
% Inputs:
%   x_low     - Low-dim data (N x 2)
%   grid_vals - Grid definition {x_grid, y_grid}
% Outputs:
%   U, X1, X2 - Potential landscape and meshgrid

% Kernel density estimation
[X1, X2] = meshgrid(grid_vals{:});
% [pdf] = ksdensity(x_low, [X1(:), X2(:)], 'Kernel','normal','Bandwidth',h);
% [pdf] = kde_batch(x_low, [X1(:), X2(:)], h);
switch model.method
    case 'adaptive'
        pdf = kde_adaptive_batch(x_low, [X1(:) X2(:)], [], model.h_i);
    case 'global'
        pdf = kde_batch(x_low, [X1(:) X2(:)], model.h);
end
% Potential calculation
P_ss = reshape(pdf, size(X1));
U = -log(P_ss + 1e-10);
U = U - min(U(:)); % Zero-base potential
% U(U==max(U(:)))=NaN;
end
function [Yhat] = eval_nars(nars, X, p_c_given_xy)
    k = length(nars);
    for c = k:-1:1
        if isempty(nars{c}) || isempty(nars{c}.X)
            Yhat_c(:,:,c) = mean(y);
            continue;
        end
        
        % Adaptive prediction using k-NN bandwidths
        if isfield(nars{c}, 'kmin') && ~isempty(nars{c}.kmin)
            [Yhat_c(:, :, c)] = NwSmoothMassCenterBatch(nars{c}.X, nars{c}.Y, nars{c}.kmin, X, nars{c}.h_i);
        else
            [Yhat_c(:, :, c)] = NwSmoothLogGaussianBatch(nars{c}.X, nars{c}.Y, nars{c}.h, X);
        end

  
    end
    p_c_given_xy=permute(p_c_given_xy,[1,3,2]);
    Yhat=sum(p_c_given_xy.* Yhat_c,3);
end

function assignments = sample_clusters(prob_mat)
% SAMPLE_CLUSTERS - Vectorized cluster sampling
%
% Inputs:
%   prob_mat - (M x K) matrix of cluster probabilities
%
% Outputs:
%   assignments - (M x 1) integer vector of sampled cluster indices

M = size(prob_mat, 1);
K = size(prob_mat, 2);
cum_probs = cumsum(prob_mat, 2);
assignments = zeros(M,1);

for m = 1:M
    r = rand();
    [~, idx] = max(find(r <= cum_probs(m,:)));
    assignments(m) = max(1, min(idx, K));
end
end


function [min_x, max_x, min_y, max_y] = get_grid_range(x_low)
min_x = min(x_low(:,1)) - 0.1;
max_x = max(x_low(:,1)) + 0.1;
min_y = min(x_low(:,2)) - 0.1;
max_y = max(x_low(:,2)) + 0.1;
end

function param=calc_norm_pdf_param(Mu, Sigma)
[R, err] = cholcov(Sigma, 0);
d=length(Mu);
param.Mu=Mu;
param.Sigma=Sigma;
param.Rinv=inv(R);
logSqrtDetSigma = sum(log(diag(R)));
param.normalize_term= + logSqrtDetSigma + d * log(2 * pi) / 2;

end


function nllh = mvnnllh_inline(param, X)
% mvnpdf_inline Compute the negative log-likelihood (NLLH) for each data point under the Gaussian distribution.
%
%   Inputs:
%       X: Data matrix (n x d).
%       param: Structure containing the Gaussian parameters:
%           - Mu: Mean vector of the Gaussian distribution.
%           - Rinv: Inverse of the Cholesky decomposition of the covariance matrix.
%           - normalize_term: Precomputed normalization term.
%
%   Output:
%       nllh: A column vector (n x 1) containing the negative log-likelihood values for each data point.
for c = length(param):-1:1

    diff = X - param{c}.Mu;  % Difference between data points and mean
    quadForm = sum((diff * param{c}.Rinv).^2, 2);  % Quadratic form (x - mu)^T Sigma^{-1} (x - mu)

    % Compute the NLLH: 0.5 * (quadForm + log(2*pi) + log(det(Sigma)))
    nllh(:,c) = 0.5 * quadForm + param{c}.normalize_term;
end
end

function x=hardmax(x)
    % the max one is 1 the other is zero
    [~,idx]=max(x,[],2);
    x=zeros(size(x));
    x(sub2ind(size(x),(1:size(x,1))',idx))=1;
end


function [cluster_grid] = compute_cluster_boundaries_projected(mu_low, Sigma_low, prior, grid_vals, X1, X2)
% Compute cluster probabilities on grid using projected GMM parameters
k = size(mu_low, 1);
grid_points = [X1(:), X2(:)];
log_p = zeros(size(grid_points, 1), k);
for c = 1:k
    % Skip clusters with zero prior
    if prior(c) <= 0, continue; end
    
    % Compute Mahalanobis distance for each grid point
    R = chol(Sigma_low(:, :, c));
    diff = bsxfun(@minus, grid_points, mu_low(c, :));
    quad_form = sum((diff / R).^2, 2);
    
    % Log probability (up to constant)
    log_p(:, c) = -0.5 * quad_form + log(prior(c)) - log(det(R));
end
% Assign clusters based on max log probability
[~, cluster_idx] = max(log_p, [], 2);
cluster_grid = reshape(cluster_idx, size(X1));
end
% function log_probs = compute_cluster_log_probs(cnars, mu_list, x)
% % LOG_PROBS = COMPUTE_CLUSTER_LOG_PROBS(cnars, mu_list, x)
% %
% % Inputs:
% %   cnars      - CNAR model structure
% %   mu_list    - {k x 1} list of cluster means
% %   x          - (M x p) current states
% %
% % Outputs:
% %   log_probs  - (M x k) log-probability for each trial and cluster
% 
% k = cnars.k;
% M = size(x,1);
% log_probs = zeros(M, k);
% log_weights = log(cnars.p_c);
% 
% for c = 1:k
%     mu = mu_list{c};
%     % diff = bsxfun(@minus, x, mu);
%     diff = x - param{c}.Mu;  % Difference between data points and mean
%     Rinv = cnars.gmm_pdf_param{c}.Rinv;
%     quad_form = sum((diff * Rinv).^2, 2);
%     normalize_term = cnars.gmm_pdf_param{c}.normalize_term;
%     log_probs(:, c) = -0.5 * quad_form - normalize_term + log_weights(c);
% end
% end
% 
% 