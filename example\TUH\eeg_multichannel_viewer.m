function eeg_multichannel_viewer(EEG, varargin)
% EEG_MULTICHANNEL_VIEWER Interactive multichannel EEG data viewer with events
%
% Usage:
%   eeg_multichannel_viewer(EEG)
%   eeg_multichannel_viewer(EEG, 'Parameter', Value, ...)
%
% Inputs:
%   EEG - EEGLAB structure containing EEG data
%
% Optional Parameters:
%   'TimeWindow'    - Time window to display in seconds (default: 10)
%   'StartTime'     - Start time in seconds (default: 0)
%   'ChannelSpacing'- Vertical spacing between channels (default: auto)
%   'AmplitudeScale'- Amplitude scaling factor (default: 1)
%   'ShowEvents'    - Show event markers (default: true)
%   'ShowGrid'      - Show time grid (default: true)
%   'FilterData'    - Apply basic filtering (default: false)
%   'Title'         - Plot title (default: auto-generated)
%
% Features:
%   - Interactive time navigation with arrow keys
%   - Zoom in/out with +/- keys
%   - Channel amplitude scaling with up/down arrows
%   - Event highlighting and labels
%   - Channel labels and time axis
%   - Seizure event highlighting in red
%
% Example:
%   eeg_multichannel_viewer(EEG_BIDS, 'TimeWindow', 15, 'ShowEvents', true);

% Parse input arguments
p = inputParser;
addRequired(p, 'EEG', @isstruct);
addParameter(p, 'TimeWindow', 10, @(x) isnumeric(x) && x > 0);
addParameter(p, 'StartTime', 0, @(x) isnumeric(x) && x >= 0);
addParameter(p, 'ChannelSpacing', [], @(x) isnumeric(x) || isempty(x));
addParameter(p, 'AmplitudeScale', 1, @(x) isnumeric(x) && x > 0);
addParameter(p, 'ShowEvents', true, @islogical);
addParameter(p, 'ShowGrid', true, @islogical);
addParameter(p, 'FilterData', false, @islogical);
addParameter(p, 'Title', '', @ischar);

parse(p, EEG, varargin{:});

% Extract parameters
time_window = p.Results.TimeWindow;
start_time = p.Results.StartTime;
channel_spacing = p.Results.ChannelSpacing;
amplitude_scale = p.Results.AmplitudeScale;
show_events = p.Results.ShowEvents;
show_grid = p.Results.ShowGrid;
filter_data = p.Results.FilterData;
plot_title = p.Results.Title;

% Validate EEG structure
if ~isfield(EEG, 'data') || ~isfield(EEG, 'srate') || ~isfield(EEG, 'times')
    error('Invalid EEG structure. Must contain data, srate, and times fields.');
end

% Get data dimensions
[n_channels, n_samples] = size(EEG.data);
total_time = n_samples / EEG.srate;

% Validate time parameters
if start_time >= total_time
    start_time = 0;
    warning('Start time exceeds data length. Reset to 0.');
end

if start_time + time_window > total_time
    time_window = total_time - start_time;
    warning('Time window adjusted to fit data length.');
end

% Create time vector
time_vector = (0:n_samples-1) / EEG.srate;

% Get sample indices for the time window
start_sample = max(1, round(start_time * EEG.srate) + 1);
end_sample = min(n_samples, round((start_time + time_window) * EEG.srate));
sample_indices = start_sample:end_sample;
time_subset = time_vector(sample_indices);

% Extract data for the time window
data_subset = EEG.data(:, sample_indices);

% Apply filtering if requested
if filter_data
    fprintf('Applying basic bandpass filter (1-50 Hz)...\n');
    for ch = 1:n_channels
        data_subset(ch, :) = bandpass(data_subset(ch, :), [1 50], EEG.srate);
    end
end

% Calculate channel spacing if not provided
if isempty(channel_spacing)
    data_range = max(data_subset(:)) - min(data_subset(:));
    channel_spacing = data_range / n_channels * 1.5;
end

% Apply amplitude scaling
data_subset = data_subset * amplitude_scale;

% Create figure
fig = figure('Name', 'EEG Multichannel Viewer', 'NumberTitle', 'off', ...
             'Position', [100, 100, 1200, 800], 'KeyPressFcn', @keyPressCallback);

% Create main axes
ax = axes('Position', [0.1, 0.1, 0.8, 0.8]);
hold on;

% Plot each channel with vertical offset
channel_offsets = (0:n_channels-1) * channel_spacing;
colors = lines(n_channels);

plot_handles = [];
for ch = 1:n_channels
    y_data = data_subset(ch, :) + channel_offsets(ch);
    h = plot(time_subset, y_data, 'Color', colors(ch, :), 'LineWidth', 0.8);
    plot_handles(ch) = h;
end

% Set axis properties
xlim([time_subset(1), time_subset(end)]);
ylim([-channel_spacing, (n_channels-1)*channel_spacing + channel_spacing]);

% Add channel labels
if isfield(EEG, 'chanlocs') && ~isempty(EEG.chanlocs)
    channel_labels = {EEG.chanlocs.labels};
else
    channel_labels = arrayfun(@(x) sprintf('Ch%d', x), 1:n_channels, 'UniformOutput', false);
end

% Add y-axis labels for channels
yticks(channel_offsets);
yticklabels(channel_labels);
ylabel('Channels');
xlabel('Time (seconds)');

% Add grid if requested
if show_grid
    grid on;
    set(ax, 'GridAlpha', 0.3);
end

% Add events if available and requested
event_handles = [];
if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    fprintf('Adding %d events to plot...\n', length(EEG.event));
    
    for i = 1:length(EEG.event)
        event_time = (EEG.event(i).latency - 1) / EEG.srate;
        
        % Only show events within the current time window
        if event_time >= time_subset(1) && event_time <= time_subset(end)
            event_type = EEG.event(i).type;
            
            % Color code events
            if contains(lower(event_type), 'sz') || contains(lower(event_type), 'seizure')
                event_color = [1, 0, 0]; % Red for seizures
                line_width = 2;
            else
                event_color = [0, 0.7, 0]; % Green for other events
                line_width = 1.5;
            end
            
            % Draw vertical line for event
            h_line = line([event_time, event_time], ylim, ...
                         'Color', event_color, 'LineWidth', line_width, ...
                         'LineStyle', '--');
            
            % Add event label
            h_text = text(event_time, max(ylim) * 0.95, event_type, ...
                         'Rotation', 90, 'FontSize', 8, 'Color', event_color, ...
                         'HorizontalAlignment', 'right', 'VerticalAlignment', 'top');
            
            event_handles = [event_handles, h_line, h_text];
        end
    end
end

% Set title
if isempty(plot_title)
    if isfield(EEG, 'setname')
        plot_title = EEG.setname;
    else
        plot_title = sprintf('EEG Data (%d channels, %.1f Hz)', n_channels, EEG.srate);
    end
end

title(sprintf('%s\nTime: %.1f - %.1f s (Window: %.1f s)', ...
      plot_title, time_subset(1), time_subset(end), time_window), ...
      'Interpreter', 'none');

% Add information text
info_text = sprintf(['Controls:\n' ...
                    '← → : Navigate time\n' ...
                    '+ - : Zoom in/out\n' ...
                    '↑ ↓ : Scale amplitude\n' ...
                    'r : Reset view\n' ...
                    'f : Toggle filter\n' ...
                    'g : Toggle grid\n' ...
                    'e : Toggle events']);

annotation('textbox', [0.02, 0.02, 0.15, 0.2], 'String', info_text, ...
           'FontSize', 8, 'BackgroundColor', 'white', 'EdgeColor', 'black');

% Store data in figure for callbacks
setappdata(fig, 'EEG', EEG);
setappdata(fig, 'current_start_time', start_time);
setappdata(fig, 'current_time_window', time_window);
setappdata(fig, 'current_amplitude_scale', amplitude_scale);
setappdata(fig, 'current_channel_spacing', channel_spacing);
setappdata(fig, 'show_events', show_events);
setappdata(fig, 'show_grid', show_grid);
setappdata(fig, 'filter_data', filter_data);
setappdata(fig, 'plot_title', plot_title);

fprintf('EEG Viewer loaded successfully!\n');
fprintf('Dataset: %s\n', plot_title);
fprintf('Channels: %d, Sampling rate: %.1f Hz\n', n_channels, EEG.srate);
fprintf('Total duration: %.1f seconds\n', total_time);
if show_events && isfield(EEG, 'event') && ~isempty(EEG.event)
    fprintf('Events: %d loaded\n', length(EEG.event));
end

end

% Keyboard callback function
function keyPressCallback(src, event)
    % Get current parameters
    EEG = getappdata(src, 'EEG');
    start_time = getappdata(src, 'current_start_time');
    time_window = getappdata(src, 'current_time_window');
    amplitude_scale = getappdata(src, 'current_amplitude_scale');
    channel_spacing = getappdata(src, 'current_channel_spacing');
    show_events = getappdata(src, 'show_events');
    show_grid = getappdata(src, 'show_grid');
    filter_data = getappdata(src, 'filter_data');
    plot_title = getappdata(src, 'plot_title');
    
    total_time = size(EEG.data, 2) / EEG.srate;
    
    % Handle key presses
    switch event.Key
        case 'rightarrow'
            % Move forward in time
            new_start = min(start_time + time_window * 0.5, total_time - time_window);
            if new_start ~= start_time
                eeg_multichannel_viewer(EEG, 'StartTime', new_start, 'TimeWindow', time_window, ...
                                       'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                       'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                       'FilterData', filter_data, 'Title', plot_title);
                close(src);
            end
            
        case 'leftarrow'
            % Move backward in time
            new_start = max(start_time - time_window * 0.5, 0);
            if new_start ~= start_time
                eeg_multichannel_viewer(EEG, 'StartTime', new_start, 'TimeWindow', time_window, ...
                                       'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                       'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                       'FilterData', filter_data, 'Title', plot_title);
                close(src);
            end
            
        case 'equal' % Plus key
            % Zoom in (decrease time window)
            new_window = max(time_window * 0.7, 1);
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', new_window, ...
                                   'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
            
        case 'hyphen' % Minus key
            % Zoom out (increase time window)
            new_window = min(time_window * 1.4, total_time);
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', new_window, ...
                                   'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
            
        case 'uparrow'
            % Increase amplitude scale
            new_scale = amplitude_scale * 1.3;
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', time_window, ...
                                   'AmplitudeScale', new_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
            
        case 'downarrow'
            % Decrease amplitude scale
            new_scale = amplitude_scale * 0.7;
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', time_window, ...
                                   'AmplitudeScale', new_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
            
        case 'r'
            % Reset view
            eeg_multichannel_viewer(EEG, 'StartTime', 0, 'TimeWindow', 10, ...
                                   'AmplitudeScale', 1, 'ShowEvents', true, 'ShowGrid', true, ...
                                   'FilterData', false, 'Title', plot_title);
            close(src);
            
        case 'f'
            % Toggle filter
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', time_window, ...
                                   'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', ~filter_data, 'Title', plot_title);
            close(src);
            
        case 'g'
            % Toggle grid
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', time_window, ...
                                   'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', show_events, 'ShowGrid', ~show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
            
        case 'e'
            % Toggle events
            eeg_multichannel_viewer(EEG, 'StartTime', start_time, 'TimeWindow', time_window, ...
                                   'AmplitudeScale', amplitude_scale, 'ChannelSpacing', channel_spacing, ...
                                   'ShowEvents', ~show_events, 'ShowGrid', show_grid, ...
                                   'FilterData', filter_data, 'Title', plot_title);
            close(src);
    end
end
