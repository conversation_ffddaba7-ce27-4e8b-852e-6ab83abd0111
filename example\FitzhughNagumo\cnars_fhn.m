%{
close all;clear
rng(0); % For reproducibility

% FitzHugh-Nagumo model parameters
sigma = 0.2;   % Noise intensity (diffusion coefficient)

%%
% a = 0.7;       % Excitability threshold
% b = 0.8;       % Recovery rate
% epsilon = 0.08; % Timescale separation
% I_ext = 0.0; % Stable Fixed Point (Spiral):
% I_ext = 0.5; % Limit Cycle (Oscillations): As in the original example
% I_ext = 0.7; % Large-Amplitude Limit Cycle: Higher current induces larger oscillations
% FitzHugh-Nagumo Parameters

type_attractor='point';%point

epsilon = 1 / 12.5;
a = 0.7;
R = 0.1;               % Scaling factor for external input

% Adjust I_ext and b based on attractor type
switch lower(type_attractor)
    case 'point'
        I_ext = 3.5;
        b = 2;
    case 'limit_cycle'
        I_ext = 1.0;
        b = 2.0;
    % case 'chaos'
    %     I_ext = 3.5;
    %     b = 2.5;
    case 'after the homoclinic bifurcation'
        I_ext = 5.45;
        b = 2;
    otherwise
        error('no such type')
end
I_ext=R*I_ext;

%%
% FHN drift functions (2D system)
drift_V = @(V,W) V - V.^3/3 - W + I_ext;
drift_W = @(V,W) epsilon*(V + a - b*W);

% Simulation parameters
dt = 1;      % Timestep %0.1
N = 10000;     % Number of points
t = (0:N-1)*dt;

% Initialize state variables
V = zeros(N,1);
W = zeros(N,1);
V(1) = -1.5;  % Initial condition
W(1) = 0;

% Heun's method integration (stochastic)
for n = 1:N-1
    % Predictor step (drift)
    k1V = drift_V(V(n), W(n)) * dt;
    k1W = drift_W(V(n), W(n)) * dt;
    
    % Predicted state (without noise)
    V_pred = V(n) + k1V;
    W_pred = W(n) + k1W;
    
    % Corrector step (drift)
    k2V = drift_V(V_pred, W_pred) * dt;
    k2W = drift_W(V_pred, W_pred) * dt;
    
    avg_dV = (k1V + k2V)/2;
    avg_dW = (k1W + k2W)/2;
    
    % Generate Wiener increment
    dW = sqrt(dt)*randn;
    
    % Update state with drift and diffusion
    V(n+1) = V(n) + avg_dV + sigma*dW;
    W(n+1) = W(n) + avg_dW;
end

% Preprocess voltage data (as before)
synth_data = V;
synth_data = synth_data - mean(synth_data);
synth_data = synth_data / max(abs(synth_data));

%%
k=2;
opt.use_threshold=false;
opt.p=5;
[cnars] = fit_cnar_unsup(synth_data,k,opt);
cnars.Fs=200;

%%
figure(101)
visualize_cnars_train(cnars)



%%
% Estimate potential landscape
sigma=0.1;
result = estimate_potential_cnar(cnars, 100, 100, sigma, 200);

% %%
figure(102); clf;
% Visualize with optional trajectory
plot_potential_pca(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off

% %%
figure(103); clf;
% Visualize with optional trajectory
plot_potential_cnar(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off

% 
% figure(104); clf;
% % Visualize with optional trajectory
% plot_potential_cnar_theo(result, []);
% set(gca, 'SortMethod', 'depth');
% % set(gca, 'SortMethod', 'childorder');
% view(2)
% axis off
%}
%%
cnars.opt.vis_y=true;
[cnars] = gen_cluster_lpr_ts_all_random(cnars, 'gaussian', 200,true);
