function comparison_report = compare_tuh_bids_consistency(tuh_file, bids_file, eeglab_path)
% COMPARE_TUH_BIDS_CONSISTENCY Compare TUH and BIDS format EEG data for consistency
%
% Usage:
%   comparison_report = compare_tuh_bids_consistency(tuh_file, bids_file, eeglab_path)
%
% Inputs:
%   tuh_file    - Path to TUH format .edf file
%   bids_file   - Path to BIDS format .edf file
%   eeglab_path - Path to EEGLAB installation (optional)
%
% Outputs:
%   comparison_report - Structure containing detailed comparison results
%
% Example:
%   report = compare_tuh_bids_consistency('aaaaaaac_s001_t000.edf', ...
%                                        'sub-000_ses-001_task-szMonitoring_run-01_eeg.edf');

% Set default EEGLAB path if not provided
if nargin < 3 || isempty(eeglab_path)
    eeglab_path = 'E:\code\tools\eeglab2025.0.0\';
end

fprintf('=== TUH vs BIDS EEG Data Consistency Check ===\n\n');

% Load both datasets
fprintf('Loading TUH format data...\n');
EEG_tuh = load_tuh_eeg(tuh_file, eeglab_path);

fprintf('\nLoading BIDS format data...\n');
EEG_bids = load_bids_eeg(bids_file, eeglab_path);

% Initialize comparison report
comparison_report = struct();
comparison_report.tuh_file = tuh_file;
comparison_report.bids_file = bids_file;
comparison_report.timestamp = datestr(now);

fprintf('\n=== COMPARISON RESULTS ===\n');

% Compare basic properties
fprintf('\n1. BASIC PROPERTIES:\n');
comparison_report.basic_properties = struct();

% Number of channels
channels_match = (EEG_tuh.nbchan == EEG_bids.nbchan);
comparison_report.basic_properties.channels_match = channels_match;
fprintf('   Channels: TUH=%d, BIDS=%d [%s]\n', EEG_tuh.nbchan, EEG_bids.nbchan, ...
        iif(channels_match, 'MATCH', 'DIFFER'));

% Number of samples
samples_match = (EEG_tuh.pnts == EEG_bids.pnts);
comparison_report.basic_properties.samples_match = samples_match;
fprintf('   Samples: TUH=%d, BIDS=%d [%s]\n', EEG_tuh.pnts, EEG_bids.pnts, ...
        iif(samples_match, 'MATCH', 'DIFFER'));

% Sampling rate
srate_match = abs(EEG_tuh.srate - EEG_bids.srate) < 0.01;
comparison_report.basic_properties.srate_match = srate_match;
fprintf('   Sampling Rate: TUH=%.2f Hz, BIDS=%.2f Hz [%s]\n', EEG_tuh.srate, EEG_bids.srate, ...
        iif(srate_match, 'MATCH', 'DIFFER'));

% Duration
duration_tuh = EEG_tuh.pnts / EEG_tuh.srate;
duration_bids = EEG_bids.pnts / EEG_bids.srate;
duration_match = abs(duration_tuh - duration_bids) < 0.1;
comparison_report.basic_properties.duration_match = duration_match;
fprintf('   Duration: TUH=%.2f s, BIDS=%.2f s [%s]\n', duration_tuh, duration_bids, ...
        iif(duration_match, 'MATCH', 'DIFFER'));

% Compare channel labels
fprintf('\n2. CHANNEL LABELS:\n');
comparison_report.channels = struct();

% Get channel labels
tuh_labels = {EEG_tuh.chanlocs.labels};
bids_labels = {EEG_bids.chanlocs.labels};

% Check if all labels match
labels_match = isequal(tuh_labels, bids_labels);
comparison_report.channels.labels_match = labels_match;
fprintf('   Channel labels match: %s\n', iif(labels_match, 'YES', 'NO'));

if ~labels_match
    fprintf('   TUH channels (%d): %s\n', length(tuh_labels), strjoin(tuh_labels, ', '));
    fprintf('   BIDS channels (%d): %s\n', length(bids_labels), strjoin(bids_labels, ', '));
    
    % Find differences
    tuh_only = setdiff(tuh_labels, bids_labels);
    bids_only = setdiff(bids_labels, tuh_labels);
    
    if ~isempty(tuh_only)
        fprintf('   Channels only in TUH: %s\n', strjoin(tuh_only, ', '));
    end
    if ~isempty(bids_only)
        fprintf('   Channels only in BIDS: %s\n', strjoin(bids_only, ', '));
    end
end

% Compare data values (if dimensions match)
fprintf('\n3. DATA VALUES:\n');
comparison_report.data = struct();

if channels_match && samples_match
    % Calculate correlation between datasets
    data_corr = corr(EEG_tuh.data(:), EEG_bids.data(:));
    comparison_report.data.correlation = data_corr;
    fprintf('   Data correlation: %.6f\n', data_corr);
    
    % Calculate RMS difference
    data_diff = EEG_tuh.data - EEG_bids.data;
    rms_diff = sqrt(mean(data_diff(:).^2));
    comparison_report.data.rms_difference = rms_diff;
    fprintf('   RMS difference: %.6f\n', rms_diff);
    
    % Calculate relative error
    data_range = max(EEG_tuh.data(:)) - min(EEG_tuh.data(:));
    relative_error = rms_diff / data_range * 100;
    comparison_report.data.relative_error_percent = relative_error;
    fprintf('   Relative error: %.4f%%\n', relative_error);
    
    % Check if data is essentially identical
    data_identical = (data_corr > 0.9999) && (relative_error < 0.01);
    comparison_report.data.essentially_identical = data_identical;
    fprintf('   Data essentially identical: %s\n', iif(data_identical, 'YES', 'NO'));
else
    fprintf('   Cannot compare data values - dimension mismatch\n');
    comparison_report.data.correlation = NaN;
    comparison_report.data.rms_difference = NaN;
    comparison_report.data.relative_error_percent = NaN;
    comparison_report.data.essentially_identical = false;
end

% Compare events (BIDS should have events, TUH might not)
fprintf('\n4. EVENTS:\n');
comparison_report.events = struct();

tuh_has_events = isfield(EEG_tuh, 'event') && ~isempty(EEG_tuh.event);
bids_has_events = isfield(EEG_bids, 'event') && ~isempty(EEG_bids.event);

comparison_report.events.tuh_has_events = tuh_has_events;
comparison_report.events.bids_has_events = bids_has_events;

fprintf('   TUH has events: %s', iif(tuh_has_events, 'YES', 'NO'));
if tuh_has_events
    fprintf(' (%d events)', length(EEG_tuh.event));
end
fprintf('\n');

fprintf('   BIDS has events: %s', iif(bids_has_events, 'YES', 'NO'));
if bids_has_events
    fprintf(' (%d events)', length(EEG_bids.event));
end
fprintf('\n');

% Overall consistency assessment
fprintf('\n=== OVERALL ASSESSMENT ===\n');
overall_consistent = channels_match && samples_match && srate_match && duration_match;
if channels_match && samples_match
    overall_consistent = overall_consistent && (comparison_report.data.correlation > 0.99);
end

comparison_report.overall_consistent = overall_consistent;
fprintf('Overall consistency: %s\n', iif(overall_consistent, 'CONSISTENT', 'INCONSISTENT'));

if overall_consistent
    fprintf('✓ The TUH and BIDS datasets appear to contain the same EEG data\n');
    if bids_has_events && ~tuh_has_events
        fprintf('✓ BIDS format provides additional event annotations\n');
    end
else
    fprintf('⚠ The datasets show significant differences\n');
end

fprintf('\nComparison completed.\n');

end

% Helper function for conditional string output
function result = iif(condition, true_val, false_val)
    if condition
        result = true_val;
    else
        result = false_val;
    end
end
