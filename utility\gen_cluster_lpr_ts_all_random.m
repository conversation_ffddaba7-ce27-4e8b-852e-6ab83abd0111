function [cnars] = gen_cluster_lpr_ts_all_random(cnars, type_innovation, N, verbose)
%{
rng(0)

if nargin < 5 || isempty(verbose)
    verbose = true;
end

% p = 32; % Embedding dimension for the model
p_candidates = 1:100;
armodel = fit_best_ar(x, p_candidates, 'arburg');
p = length(armodel.A);
% p=round(p/2);

% Generate candidate bandwidths
hlist = get_hList(50, [(max(x) - min(x)) / 50, (max(x) - min(x)) / 1], @linspace);

% Parameters for nars_gmm_nw
k = 2; % Number of GMM clusters
% warning('auto select cluster number')
% k = []; % Number of GMM clusters

max_iter = 5; % Max iterations for cluster refinement


% Convert to GPU arrays if needed
% [x, p, k, hlist, max_iter, N] = convert_to_gpuarray(x, p, k, hlist, max_iter, N);

% Step 1: Get cluster models and GM model (0 innovation first to estimate residual)
cnars = cluster_nar_gmm_nw_joint_random(x, p, hlist, k, max_iter);
%}

type_init_sim='start';

% Estimate residuals to determine noise scale for innovation, since in
% cluster_nar_gmm_nw_joint_random may not able to handle full data
if nargin < 4 || isempty(verbose)
    verbose = true;
end

k=length(cnars.p_c);
p=cnars.p;
x=cnars.x;

% x_pred_one_step = one_step_ahead_forecast_joint(x, 0, p, nars, gmm);
% residuals = x - x_pred_one_step;
noise_scale = 1;
Nsim=1;
% warning('zero noise')
% noise_scale = 0;

% warning('double noise')
% noise_scale = 2;

% Generate future innovation depending on noise 'type'
% sigma = noise_scale * var(residuals, [], "all", "omitnan");

% warning('use square noise')
% sigma=sigma^2;

switch lower(type_innovation)
    case 'free'
        innovation=zeros(N,1);
    case 'residual'
        innovation = cnars.res;
        % innovation(1:end-p) = innovation(p+1:end);
        innovation(1:p,:) = [];
        if N > size(innovation, 1)
            innovation = padarray(innovation, double(gather(N - size(innovation, 1))), "circular", 'post');
        else
            innovation = innovation(1:N,:,:);
        end
        %{
        x_pred_one_step = one_step_ahead_forecast(x, 0, p, cnars.nars, cnars.gmm);
        residuals = x - x_pred_one_step;
        noise_scale = 1;
        innovation = residuals;
        % innovation(1:end-p) = innovation(p+1:end);
        innovation(1:p) = [];
        innovation = padarray(innovation, double(gather(N - size(innovation, 1))), "circular", 'post');
        %}
    case 'permuteres'
        innovation = cnars.res;
        % innovation(1:end-p) = innovation(p+1:end);
        innovation(1:p) = [];
        innovation = padarray(innovation, double(gather(N - size(innovation, 1))), "circular", 'post');
        idx_rand=randperm(size(innovation,1),size(innovation,1));
        innovation = innovation(idx_rand);
    case 'kde'
        pd = fitdist(cnars.res, 'Kernel');
        innovation = random(pd, N, 1);
        %{
        x_pred_one_step = one_step_ahead_forecast(x, 0, p, cnars.nars, cnars.gmm);
        residuals = x - x_pred_one_step;

        pd = fitdist(residuals, 'Kernel');
        innovation = random(pd, N, 1);

        % noise_scale = 1;
        % innovation = residuals;
        % % innovation(1:end-p) = innovation(p+1:end);
        % innovation(1:p) = [];
        % innovation = padarray(innovation, double(gather(N - size(innovation, 1))), "circular", 'post');
        %}

    case 'kde_class'
        for c=cnars.k:-1:1
            pd = fitdist(cnars.res_c(:,c), 'Kernel');
            innovation(:,c) = noise_scale.*random(pd, N, 1);
        end
    case 'gaussian'
        innovation = randn(N, 1) * cnars.sigma_y;
    case 'pearson3'
        innovation = gen_pearson3_rnd(0, cnars.sigma_y.^2, 4, [N, 1]);
    case 'ar_gaussian'
        innovation = sqrt(cnars.sigma_y) .* ar_noise(N, 1, 'gaussian');
    case 'ar_pearson3'
        innovation = sqrt(cnars.sigma_y) .* ar_noise(N, 1, 'pearson3');
    case 'ar_kde'
        pd = fitdist(cnars.res, 'Kernel');
        innovation = random(pd, N, 1);
        innovation = sqrt(cnars.sigma_y) .* ar_noise(N, 1, innovation);
    case 'frrb2012_kde'
        res=cat(1,nan(p,size(x,2)),cnars.res);
        residuals=res./x;
        residuals(x<1)=res(x<1);
        % ita=mean(residuals);
        pd = fitdist(residuals, 'Kernel');
        innovation = random(pd, N, 2, Nsim); 
        rho=0.61;
        ita=45./cnars.Fs;
        % cnars.g=@(X,yq,innovation) ita*((1-rho)*innovation(:,1,:)+rho.*yq.*innovation(:,2,:));
        cnars.g=@(X,yq,innovation) ita*((1-rho)*innovation(:,1,:)+rho.*sqrt(yq(1).^2+yq(2).^2).*innovation(:,2,:));
    case 'frrb2012'
        % res=cat(1,nan(p,1),cnars.res);
        % residuals=res./x;
        % residuals(x<1)=res(x<1);
        % ita=mean(residuals);
        % pd = fitdist(residuals, 'Kernel');
        % innovation = cnars.sigma_y.*randn(N, 2,Nsim); 
        rho=0.61;%0.61
        % ita=5;
        dt=1./cnars.Fs;
        innovation = sqrt(dt).*randn(N, 2,Nsim); 
        ita=45*sqrt(dt); %1
        % cnars.g=@(X,yq,innovation,theta) ita*((1-p_innovation)*innovation(:,1,:)+2*p_innovation.*yq./cos(theta).*innovation(:,2,:));
        cnars.g=@(X,yq,innovation) ita*((1-rho)*innovation(:,1,:)+rho.*sqrt(yq(1).^2+yq(2).^2).*innovation(:,2,:));
    case 'frrb2012r'
        % res=cat(1,nan(p,1),cnars.res);
        % residuals=res./x;
        % residuals(x<1)=res(x<1);
        % ita=mean(residuals);
        % pd = fitdist(residuals, 'Kernel');
        % innovation = cnars.sigma_y.*randn(N, 2,Nsim); 
        rho=0.61;
        % ita=5;
        innovation = randn(N, 2,Nsim); 
        ita=2;
        cnars.g=@(X,yq,innovation,theta) ita*((1-rho)*innovation(:,1,:)+2*rho.*yq./cos(theta).*innovation(:,2,:));
        % cnars.g=@(X,yq,innovation) ita*((1-p_innovation)*innovation(:,1,:)+p_innovation.*yq.*innovation(:,2,:));

    case 'frrb2012x'
        % res=cat(1,nan(p,1),cnars.res);
        % residuals=res./x;
        % residuals(x<1)=res(x<1);
        % ita=mean(residuals);
        % pd = fitdist(residuals, 'Kernel');
        % innovation = cnars.sigma_y.*randn(N, 2,Nsim); 
        rho=0.61;
        % ita=5;
        innovation = randn(N, 2,Nsim); 
        ita=1;
        cnars.g=@(X,yq,innovation,r,theta) ita*((1-rho)*innovation(:,1,:)+2*rho.*r.*innovation(:,2,:));
        % cnars.g=@(X,yq,innovation) ita*((1-p_innovation)*innovation(:,1,:)+p_innovation.*yq.*innovation(:,2,:));
    otherwise
        error('Unknown noise type.');
end

% innovation = convert_to_gpuarray(innovation);

%% Step 2: Fit model and forecast using nars_gmm_nw (full mixture model)
% [x_pred_full, idx_forecast_full, ~, nars, gmm, idx_train] = cluster_nar_gmm_nw(x, innovation, p, hlist, k, max_iter, false);
% forcast_func=@forecast_cluster_nar_gmm_nw_vis;
forcast_func=@forecast_cnar_vis;

init_state=init_state_sim(x,p,type_init_sim);
cnars.verbose=verbose;
[x_pred_full, idx_forecast_full,p_stay_pred_full] = forcast_func(cnars, init_state, innovation,type_innovation, false);
cnars.verbose=false;

if isempty(k)
    k = length(nars);
end

for c = k:-1:1
    cnars_sub=cnars;
    cnars_sub.nars=cnars.nars(c);
    if ~isempty(cnars_sub.nars{1})
        [x_pred_cluster(:,:, c)] = forcast_func(cnars_sub, init_state, innovation,type_innovation, false);
        idx_forecast_cluster(:, c) = c*ones(size(innovation,1),1);
    else
        x_pred_cluster(:,:, c) = NaN;
        idx_forecast_cluster(:, c) = NaN;
    end

end

x_pred_full = gather(x_pred_full);
x_pred_cluster = gather(x_pred_cluster);
x_pred = cat(3,x_pred_full, x_pred_cluster);
idx_pred = [idx_forecast_full, idx_forecast_cluster];

%% Step 3: noise free
[x_free_full, idx_free_full,p_stay_free_full] = forcast_func(cnars, init_state, zeros(size(innovation), 'like', innovation),type_innovation, false);

for c = k:-1:1
    cnars_sub=cnars;
    cnars_sub.nars=cnars.nars(c);
    if ~isempty(cnars_sub.nars{1})
        [x_free_cluster(:,:, c)] = forcast_func(cnars_sub, init_state, zeros(size(innovation), 'like', innovation),type_innovation, false);
        idx_free_cluster(:, c) = c*ones(size(innovation,1),1);
    else
        x_free_cluster(:,:, c) = NaN;
        idx_free_cluster(:, c) = NaN;
    end

end

x_free_full = gather(x_free_full);
x_free_cluster = gather(x_free_cluster);
x_free = cat(3,x_free_full, x_free_cluster);
idx_free = [idx_free_full, idx_free_cluster];

%% save to structure
% cnars.x = x;
% cnars.Fs = Fs;
% cnars.N = N;
cnars.verbose = verbose;
% cnars.p = p;
% cnars.k = k;
cnars.noise_scale = noise_scale;
cnars.type_innovation = type_innovation;
cnars.innovation = innovation;
cnars.x_pred = x_pred;
cnars.idx_pred = idx_pred;
cnars.p_stay_pred_full=p_stay_pred_full;
cnars.x_free = x_free;
cnars.idx_free = idx_free;
cnars.p_stay_free_full=p_stay_free_full;

% Visualize if verbose
% if verbose
%     % figure(201)
%     % visualize_results(x, Fs, idx_train, x_pred, idx_pred, p, k, nars, gmm)
%     % figname = [cluster_lpr_ts_all, '_', parent_mfilename(), '_', type];
%     % sgtitle(figname)

%     % figure(202)
%     % visualize_3d_trajectories(x_pred, p, idx_pred, Fs)
%     % figure
%     % visualize_energy_landscape(x, p, k, nars, gmm, Fs)
% end

end

%% one_step_ahead_forecast_joint
function y_pred_one_step = one_step_ahead_forecast_joint(x, n_train, p, nars, gmm)
% ONE_STEP_AHEAD_FORECAST performs one-step-ahead prediction using a joint GMM+NW model.
% For each test point, we combine GMM posterior p(c|x) with NW likelihood p(y|x,c)
% to determine the cluster assignment.
use_joint_model=false;

x_test = x(n_train + 1:end);
n_test = length(x_test);
y_pred_one_step = zeros(n_test, 1);

if use_joint_model
    k = length(nars);
    % Precompute sigma for each cluster model if not empty
    sigma_c = nan(k,1);
    for c = 1:k
        if ~isempty(nars{c})
            cX = nars{c}.X;
            cY = nars{c}.Y;
            h = nars{c}.h;
            y_fit_cluster = NwSmoothInline(cX, cY, h, cX);
            residuals_c = cY - y_fit_cluster;
            s = std(residuals_c);
            if s < 1e-8
                s = 1e-8; % Avoid zero variance
            end
            sigma_c(c) = s;
        end
    end
    for i = 1:n_test
        if i < p + 1
            y_pred_one_step(i) = NaN;
        else
            % Current input vector for forecasting
            current_idx = n_train + i;
            input_vec = x(current_idx - p:current_idx - 1)';

            % The true next value is x_test(i), we use it to form p(y|x,c)
            y_true = x_test(i);

            % 1) Get GMM posterior p(c|x)
            postP = posterior(gmm, input_vec); % p(c|x)

            % 2) Compute NW likelihood p(y|x,c) for each cluster c
            NW_like = zeros(k,1);
            for c = 1:k
                if ~isempty(nars{c})
                    cX = nars{c}.X;
                    cY = nars{c}.Y;
                    h = nars{c}.h;
                    yq_c = NwSmoothInline(cX, cY, h, input_vec);

                    % Compute Gaussian likelihood for y_true:
                    % p(y|x,c) = N(y_true; yq_c, sigma_c(c)^2)
                    s = sigma_c(c);
                    res = y_true - yq_c;
                    NW_like(c) = (1/(sqrt(2*pi)*s)) * exp(-0.5*(res^2)/(s^2));
                else
                    % If cluster is empty, no contribution
                    NW_like(c) = 0;
                end
            end

            % 3) Combine GMM and NW to get joint posterior p(c|x,y) ∝ p(c|x)*p(y|x,c)
            combined = postP .* NW_like;
            % If all combined are zero (unlikely), fallback to GMM alone
            if all(combined == 0)
                [model, ~] = selectNonEmptyModel(postP, nars);
            else
                [~, cluster_id] = max(combined);
                model = nars{cluster_id};
            end

            % 4) Predict using the selected model
            if ~isempty(model)
                cX = model.X;
                cY = model.Y;
                h = model.h;
                yq = NwSmoothInline(cX, cY, h, input_vec);
            else
                % If no model selected (all empty?), fallback to mean of training outputs
                allY = cell2mat(cellfun(@(m)m.Y, nars(~cellfun(@isempty,nars)),'UniformOutput',false));
                yq = mean(allY);
            end
            y_pred_one_step(i) = yq;
        end
    end
else
    for i = 1:n_test

        if i < p + 1
            y_pred_one_step(i) = NaN;
        else
            current_idx = n_train + i;
            input_vec = x(current_idx - p:current_idx - 1)';

            [postP] = posterior(gmm, input_vec);
            [model] = selectNonEmptyModel(postP, nars);

            h = model.h;
            cX = model.X;
            cY = model.Y;

            yq = NwSmoothInline(cX, cY, h, input_vec);
            y_pred_one_step(i) = yq;
        end

    end
end

end

function [model, cluster_id] = selectNonEmptyModel(postP, nars)
% selectNonEmptyModel
valid_idx = find(~cellfun(@isempty, nars));

if isempty(valid_idx)
    model = [];
    cluster_id = [];
    return;
end

[~, max_ind_in_valid] = max(postP(valid_idx));
cluster_id = valid_idx(max_ind_in_valid);
model = nars{cluster_id};
end

%%

function [yq, L, dbg] = NwSmoothInline(x, y, h, xq)

if nargin < 4 || isempty(xq)
    xq = x;
end

[n, dx] = size(x);
[nq, ~] = size(xq);

if size(x, 2) > 1
    x = reshape(x, [n, 1, dx]);
end

if size(xq, 2) > 1
    xq = reshape(xq, [nq, 1, dx]);
end

h = permute(h, [dx + 2:-1:1]);

D = x - permute(xq, [2, 1, 3]);
% Dkn=squeeze(gauss_kernel(D,h));
% Dkn=squeeze(epan_kernel(D,h));
% Dkn = squeeze(gaussian_kernel(D, h));
Dkn = gaussian_kernel(D, h);
% Dx=ones(n,1);
% e1=[1;zeros(n-1,1)];
% L=e1*(Dx'*Dkn*Dx./Dx'*Dkn);
% yq=L*y;
% yq = squeeze(sum(Dkn .* y, 1) ./ sum(Dkn, 1));
% yq = yq';
yq = (sum(Dkn .* y, 1) ./ sum(Dkn, 1))';
dbg.s = sum(Dkn, 1);

if nargout > 1
    L = Dkn ./ sum(Dkn, 1).'; %
    %       L=diag(Dkn)./sum(Dkn,1).';
end

end

function u = epan_kernel(u, b)
u = u ./ b;
% u=max(eps,3/4*(1-sum(u.^2,3)));
u = max(0, 3/4 * (1 - sum(u .^ 2, 3)));
end

% function w = gauss_kernel(u,b)
% u=u./b;
% w = exp(-0.5*sum(u.^2,3));
% end

function u = gaussian_kernel(u, b)
u = u ./ b;
u = (1 / sqrt(2 * pi)) * exp(-0.5 * sum((u .^ 2), 3));
end

function [X] = ts2hankel(x, p)
N = length(x);
X = hankel(x(1:p), x(p:N));
end

function init_stat=init_state_sim(x,p,type_init)
% Embed the time series to determine the initial state
% X = ts2hankel(x, p + 1)';
Nch=size(x,2);
H=mvts2hankel(x,p+1);
inputs = H(:, 1:Nch*p); % Past values

[inputs, p] = convert_to_gpuarray(inputs, p);
% Set initial state
if nargin < 6 || isempty(type_init)
    type_init = 'start';
end

if isCharString(type_init)
    if strcmpi(type_init,'end')
        init_stat = inputs(end, :);
    elseif strcmpi(type_init,'start')
        init_stat = inputs(1, :);
    else
        error('init_state must be "start", "end", or a numeric vector of length p.');
    end
elseif isnumeric(type_init) && length(type_init) == p
    init_stat = init_state;
else
    error('init_state must be "start", "end", or a numeric vector of length p.');
end
end

%% one_step_ahead_forecast
function y_pred_one_step = one_step_ahead_forecast(x, n_train, p, nars, gmm)
% ONE_STEP_AHEAD_FORECAST performs one-step-ahead prediction to estimate residuals.
x_test = x(n_train + 1:end);
n_test = length(x_test);
y_pred_one_step = zeros(n_test, 1);

for i = 1:n_test

    if i < p + 1
        y_pred_one_step(i) = NaN;
    else
        current_idx = n_train + i;
        input_vec = x(current_idx - p:current_idx - 1)';

        [postP] = posterior(gmm, input_vec);
        [model] = selectNonEmptyModel(postP, nars);

        h = model.h;
        cX = model.X;
        cY = model.Y;

        yq = NwSmoothInline(cX, cY, h, input_vec);
        y_pred_one_step(i) = yq;
    end

end

end

