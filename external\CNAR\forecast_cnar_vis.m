function [y_pred, idx_pred, p_stay] = forecast_cnar_vis(cnars, init_state, innovation, type_innovation, update)
% FORECAST_CLUSTER_NAR_GMM_NW_JOINT - Enhanced version with visualization
cnars.opt = set_defaults(cnars.opt, ...
    'vis_dim', 3);

% Original initialization code
nars = cnars.nars;
gmm = cnars.gmm;
k = length(nars); % Number of clusters
if k>1
    for c=k:-1:1
        cnars.gmm_pdf_param{c}=calc_norm_pdf_param(gmm.mu(c,:),gmm.Sigma(:,:,c));
    end
end
alpha=3;
cnars.xmax = alpha*max(cnars.x);
cnars.xmin = alpha*min(cnars.x);
Nch=size(cnars.x,2);
d=size(init_state,2);%
p=d/Nch;
num_forecasts = length(innovation);
y_pred = nan(num_forecasts, d, 'like', innovation);
idx_pred = zeros(num_forecasts, 1, 'like', innovation);
p_stay = zeros(num_forecasts, 1, 'like', innovation);
vis_y = isfield(cnars.opt, 'vis_y') && cnars.opt.vis_y;

% Visualization setup
if cnars.verbose
    % Prepare memory points visualization
    all_X = cell2mat2(cellfun(@(m) m.X, nars(~cellfun(@isempty, nars)), 'UniformOutput', false));
    all_y = cell2mat2(cellfun(@(m) m.Y, nars(~cellfun(@isempty, nars)), 'UniformOutput', false));
    all_X_mean=mean(all_X);
    cluster_ids = cell2mat(arrayfun(@(c) repmat(c, size(nars{c}.X,1), 1),...
        find(~cellfun(@isempty, nars)), 'UniformOutput', false));

    % % PCA projection for 3D visualization
    % [coeff, ~, ~, ~, explained] = pca(all_X);
    % viz_data = all_X * coeff(:,1:3);
    % Modified visualization data selection
    if vis_y
        % Validate Y dimensions
        % if size(all_y, 2) < 3
        %     error('Y must have at least 3 dimensions for this visualization');
        % end
        if size(all_y, 2) < 3
            if isfield(cnars.opt,'potential')&& ~isempty(cnars.opt.potential)
                all_y(:,3)=cnars.opt.potential(all_y(:,1),all_y(:,2));
            else
                all_y(:,3)=0;
            end
        
            viz_data = all_y(:,1:3);
            % viz_label = 'Output Dimensions (Y_{1:3})';
        else
            % Original PCA projection
            [coeff, ~, ~, ~, explained] = pca(all_X);
            Proj=coeff(:, 1:cnars.opt.vis_dim);
            viz_data = all_X * Proj;
            % viz_label = sprintf('Input PCA (%.1f%% Variance)', sum(explained(1:3)));
        end
    end
    %%
    fig_line = figure(102);clf;    hold on;
    hFL=cell(k+1,1);
    hFL{1} = animatedline('Color',[0.8,0.8,0.8],'LineStyle','-','Marker','none'); % Handles streaming data
    kcolor=lines(k+1);
    for ik=1:k
        hFL{ik+1} = animatedline('Color',kcolor(ik+1,:),'LineStyle','none','Marker','.','MarkerSize',15); % Handles streaming data
    end

    %%
    % Create figure with subplots
    fig = figure(100);clf;    hold on;

    configure_figure_theme(fig, cnars);

    viz_data=gather(viz_data);
    % Subplot 1: Memory clusters
    % subplot(1,3,1);
    % In the visualization setup section:
    hold on;
    
    % Plot potential surface FIRST
    if vis_y && isfield(cnars.opt,'potential') && ~isempty(cnars.opt.potential)
        plot_potential_surface(cnars, viz_data);
    end
    
    % Then plot scatter points
    scatter3(viz_data(:,1), viz_data(:,2), viz_data(:,3), 10, cluster_ids, 'filled','AlphaData',0.9);
    % Replace the original scatter3 line
    color_matrix = get_cluster_colors(cluster_ids);
    scatter3(viz_data(:,1), viz_data(:,2), viz_data(:,3), 10, color_matrix, 'filled','AlphaData',0.9);
    
    cnars.opt = set_defaults(cnars.opt, ...
    'xlim',[min(viz_data(:,1)),max(viz_data(:,1))],...
    'ylim',[min(viz_data(:,2)),max(viz_data(:,2))],...
    'zlim',[min(viz_data(:,3)),max(viz_data(:,3))]);


    view(4,-11)
    % title(sprintf('Memory Clusters (%.1f%% Variance)', sum(explained(1:3))));
    colormap(jet(k)); colorbar;
    axis tight; grid on;
    if isfield(cnars.opt,'xlim')&& ~isempty(cnars.opt.xlim)
        xlim([cnars.opt.xlim])
    end
    if isfield(cnars.opt,'ylim')&& ~isempty(cnars.opt.ylim)
        ylim([cnars.opt.ylim])
    end
    if isfield(cnars.opt,'zlim')&& ~isempty(cnars.opt.zlim)
        zlim([cnars.opt.zlim])
    end
    % Subplot 2: Forecast trajectory
    % subplot(1,3,2);
    % h_traj = animatedline('Color', 'k', 'Marker', '-', 'LineWidth', 1.5);
    % hF{1} = animatedline('Color',[0.8,0.8,0.8],'LineStyle','-','Marker','none'); % Handles streaming data
    % figure(2);clf; hold on;
    hF=cell(k+1,1);
    hF{1} = animatedline('Color',[0.8,0.8,0.8],'LineStyle','-','Marker','none'); % Handles streaming data
    kcolor=lines(k+1);
    for ik=1:k
        hF{ik+1} = animatedline('Color',kcolor(ik+1,:),'LineStyle','none','Marker','.','MarkerSize',15); % Handles streaming data
    end
    % Add this after h_weights initialization
    h_links = animatedline('Color', 'm', 'LineStyle', '--', 'LineWidth', 1.5, ...
        'Marker', 'none', 'MaximumNumPoints', 2);
    if  isfield(cnars.opt,'black_bg') && cnars.opt.black_bg
        h_current = scatter3([], [], [], 120, 'r', 'filled', ...
            'MarkerEdgeColor', 'w', 'LineWidth', 2.5, ...
            'CData', [1 0 0]); % Bright red marker with black border
    else
        h_current = scatter3([], [], [], 120, 'r', 'filled', ...
            'MarkerEdgeColor', 'k', 'LineWidth', 2.5, ...
            'CData', [1 0 0]); % Bright red marker with black border
    end


    % h_current = scatter3([], [], [], 100, 'r', 'filled');
    % title('Forecast Trajectory');

    % Subplot 3: Influential points
    % subplot(1,3,3);
    h_weights = scatter3([], [], [], 50, 'g', 'filled', 'MarkerEdgeColor', 'k');
    % title('Top Influential Memory Points');

    % Initialize video writer
    video_writer = [];
    if isfield(cnars.opt, 'save_video') && cnars.opt.save_video
        % Set video parameters
        filename = getfield(cnars.opt, 'video_filename');
        frame_rate = 30;

        video_writer = VideoWriter(filename, 'MPEG-4');
        video_writer.FrameRate = frame_rate;
        open(video_writer);

        % Store initial camera position
        [initial_az, initial_el] = view;
        az_per_frame = 360 / num_forecasts; % Full rotation over all frames
    end
end






% Main forecasting loop
for i = 1:num_forecasts
    % Original prediction logic
    [y_pred(i,:), idx_pred(i), p_c_given_xy,weights] = ...
        pred_nars_gmm(init_state, cnars, innovation(i,:,:), type_innovation, i);

    % Store cluster persistence probability
    if i > 1
        p_stay(i) = p_c_given_xy(idx_pred(i-1));
    else
        p_stay(i) = nan;
    end
    
    % Visualization updates
    if cnars.verbose
        %%
        figure(102)
        addpoints(hFL{1}, i, y_pred(i,end));
        addpoints(hFL{idx_pred(i)+1}, i, y_pred(i,end)); % Append new point
        %%
        figure(100)
        % Project current state to PCA space
        % current_pca = (init_state - mean(all_X)) * Proj;
        if vis_y
            current_proj = gather(y_pred(i,:));
            if length(current_proj) < 3
                if isfield(cnars.opt,'potential')&& ~isempty(cnars.opt.potential)
                    current_proj = [current_proj, cnars.opt.potential(current_proj(1),current_proj(2))];
                else
                    current_proj = [current_proj, zeros(1,3-length(current_proj))]; %#ok<*AGROW>
                end

            else
                % Original PCA projection
                current_proj = gather((init_state - all_X_mean) * Proj);
            end
        end

        % Update visualization with current_proj
        addpoints(hF{1}, current_proj(1), current_proj(2), current_proj(3));
        addpoints(hF{idx_pred(i)+1}, current_proj(1), current_proj(2), current_proj(3)); % Append new point
        % set(h_current, 'XData', current_proj(1), 'YData', current_proj(2), 'ZData', current_proj(3));

        if ~isempty(current_proj)
            set(h_current, 'XData', current_proj(1), ...
                'YData', current_proj(2), ...
                'ZData', current_proj(3), ...
                'Visible', 'on');
        else
            set(h_current, 'Visible', 'off');
        end


        % Add validation checks for weights and indices
        weights = gather(weights);

        % Check for non-zero weights
        valid_weights = weights > eps(1);  % Threshold at machine epsilon
        if any(valid_weights)
            % Get valid indices and weights
            valid_idx = find(valid_weights);
            valid_weights = weights(valid_weights);

            % Ensure we don't request more points than available
            num_points = min(10, length(valid_idx));
            [~, sorted_idx] = sort(valid_weights, 'descend');
            top_idx = valid_idx(sorted_idx(1:num_points));

            % Verify cluster data dimensions
            cluster_data_size = size(nars{idx_pred(i)}.X, 1);
            if ~isempty(top_idx) && max(top_idx) <= cluster_data_size
                if d==3
                    pt=gather(nars{idx_pred(i)}.Y(top_idx,:));
                    % Update influential points display
                    set(h_weights, 'XData', pt(:,2), ...
                        'YData', pt(:,2), ...
                        'ZData', pt(:,2));
                elseif  isfield(cnars.opt,'potential')&& ~isempty(cnars.opt.potential)
                    set(h_weights, 'XData', pt(:,2), ...
                        'YData', pt(:,2), ...
                        'ZData', gather(cnars.opt.potential(pt)));
                else
                    pt = gather((nars{idx_pred(i)}.Y(top_idx, :) - all_X_mean) * Proj);
                    if cnars.opt.vis_dim==3
                    % Update influential points display
                    set(h_weights, 'XData', pt(:,1), ...
                        'YData', pt(:,2), ...
                        'ZData', pt(:,3));
                    else
                        % Update influential points display
                        set(h_weights, 'XData', pt(:,1), ...
                            'YData', pt(:,2));
                    end
                end
            else
                % Reset display if invalid indices
                set(h_weights, 'XData', [], 'YData', [], 'ZData', []);
            end
        else
            % Handle zero-weight scenario
            warning('No significant influential points at step %d', i);
            set(h_weights, 'XData', [], 'YData', [], 'ZData', []);
        end


        % Replace the connection line code with:
        % Delete previous connection lines
        if ~isempty(h_links)
            delete(h_links)
            h_links = gobjects(0);
        end

        % Create new connections
        if ~isempty(top_idx) && ~isempty(current_proj)
            for ilink = 1:length(top_idx)
                if d==3
                    % Get influential point coordinates
                    % pt = gather(nars{idx_pred(i)}.Y(top_idx(ilink), :));
                    % Create individual line for each connection
                    h_links(end+1) = plot3([current_proj(1), pt(ilink,1)], ...
                        [current_proj(2), pt(ilink,2)], ...
                        [current_proj(3), pt(ilink,3)], ...
                        'Color', [1 0 1 0.3], 'LineStyle', '--', ...
                        'LineWidth', 1, 'Marker', 'none');
                elseif isfield(cnars.opt,'potential')&& ~isempty(cnars.opt.potential)
                    % Get influential point coordinates
                    % pt = gather(nars{idx_pred(i)}.Y(top_idx(ilink), :));
                    h_links(end+1) = plot3([current_proj(1), pt(ilink,1)], ...
                        [current_proj(2), pt(ilink,2)], ...
                        [cnars.opt.potential(current_proj(1),current_proj(2)), cnars.opt.potential(pt(ilink,:))], ...
                        'Color', [1 0 1 0.3], 'LineStyle', '--', ...
                        'LineWidth', 1, 'Marker', 'none');
                else
                    % Get influential point coordinates
                    % pt = gather((nars{idx_pred(i)}.Y(top_idx(ilink), :) - all_X_mean) * Proj);
                    % Create individual line for each connection
                    if cnars.opt.vis_dim==3
                        h_links(end+1) = plot3([current_proj(1), pt(ilink,1)], ...
                            [current_proj(2), pt(ilink,2)], ...
                            [current_proj(3), pt(ilink,3)], ...
                            'Color', [1 0 1 0.3], 'LineStyle', '--', ...
                            'LineWidth', 1, 'Marker', 'none');
                    else
                        h_links(end+1) = plot3([current_proj(1), pt(ilink,1)], ...
                            [current_proj(2), pt(ilink,2)], ...
                            [0,0], ...
                            'Color', [1 0 1 0.3], 'LineStyle', '--', ...
                            'LineWidth', 1, 'Marker', 'none');
                    end
                end
            end
        end


        % Add contextual annotations
        % if i == 1
        %     annotation(fig, 'textbox', [0.05 0.9 0.2 0.1], 'String', ...
        %         sprintf('Cluster: %d\nInnovation: %.2f', idx_pred(i), innovation(i,:)), ...
        %         'EdgeColor', 'none');
        % end

        drawnow limitrate;
        if isfield(cnars.opt,'draw_interval') && cnars.opt.draw_interval
            pause(cnars.opt.draw_interval)
        end
        % Capture video frame
        if isfield(cnars.opt,'save_video') && cnars.opt.save_video && ~isempty(video_writer)
            frame = getframe(fig);
            writeVideo(video_writer, frame);

            % Update camera view
            current_az = initial_az + (i-1) * az_per_frame;
            view(current_az, initial_el);
        end
    end

    % Update state for next iteration
    % init_state = [init_state(Nch+1:end), y_pred(i,:)];
    init_state=y_pred(i,:);
end
% Clean up video writer
if ~isempty(video_writer)
    close(video_writer);
end
% Add final visualization elements
% if cnars.verbose
%     % Add legend and final touches
%     subplot(1,3,1);
%     legend({'Memory points'}, 'Location','best');
%     subplot(1,3,2);
%     legend({'Forecast path', 'Current state'}, 'Location','best');
%     subplot(1,3,3);
%     legend({'Influential points'}, 'Location','best');
%
%     % Create linked rotation between subplots
%     hLink = linkprop([get(fig,'Children')], {'CameraPosition','CameraUpVector'});
%     setappdata(fig, 'RotationLink', hLink);
% end
end



% Helper functions


function [yhat,cluster_assign, p_c_given_xy,L] = ...
    pred_nars_gmm(X, cnars, innovation,type_innovation,istep)

% Initialize cluster standard deviations
nars=cnars.nars;

if length(nars) > 1

    nllh_x_given_c = mvnnllh_inline(cnars.gmm_pdf_param, X);
    nllh_xy_given_c = nllh_x_given_c;

    % NLLH for p_c_given_xy (posterior probabilities)
    nllh_xyc = nllh_xy_given_c - log(cnars.p_c);

    % Log-Sum-Exp trick to avoid numerical overflow % nllh_sum = log(sum(exp(nllh_p_xyc), 2) + 1e-8);
    max_nllh_p_xyc = max(nllh_xyc, [], 2);  % Find the max value in each row
    nllh_sum = log(sum(exp(nllh_xyc - max_nllh_p_xyc), 2)) + max_nllh_p_xyc;
    nllh_c_given_xy = nllh_xyc - nllh_sum;

    % Cluster assignment based on NLLH (minimize NLLH)
    nllh_c_given_xy = mean(nllh_c_given_xy, 3);  % Average across any third dimension
    [~, cluster_assign] = min(nllh_c_given_xy, [], 2);
    % yhat=yhat_c(:,cluster_assign);
    p_c_given_xy=exp(-nllh_c_given_xy);
    % p_c_given_xy=p_c_given_xy./sum(p_c_given_xy,2);
    if any(isinf(p_c_given_xy))
        p_c_given_xy=zeros(size(p_c_given_xy));
        p_c_given_xy(cluster_assign)=1;
    else
        p_c_given_xy=p_c_given_xy./sum(p_c_given_xy,2);
    end
else
    cluster_assign=1;
    p_c_given_xy=1;
end

if ~cnars.opt.issoft
    p_c_given_xy=hardmax(p_c_given_xy);
end

[yq,L] = eval_nars(nars, X, p_c_given_xy);



if strcmpi(type_innovation,'frrb2012') || strcmpi(type_innovation,'frrb2012_kde')
    Nch=size(yq,2);
    yq_last=X(1,end-Nch+1:end);
    theta=angle(yq_last(1)+1i*yq_last(2));
    dW_r=cnars.g(X,yq_last,innovation);
    yhat= yq+[dW_r*cos(theta),dW_r*sin(theta)];
elseif strcmpi(type_innovation,'frrb2012x')
    Z=hilbert(X);
    r=abs(Z);
    r=r(end);
    r = max(min(r, cnars.xmax), cnars.xmin);
    theta=unwrap(angle(Z));
    theta=theta(end)+mean(diff(theta));
    yhat = yq+cnars.g(X,yq,innovation,r,theta)*cos(theta);
elseif strcmpi(type_innovation,'frrb2012r')
    theta=unwrap(angle(hilbert(X)));
    theta=theta(end)+mean(diff(theta));
    yhat = (yq./cos(theta)+cnars.g(X,yq,innovation,theta)).*cos(theta);
elseif strcmpi(type_innovation,'kde_class')
    yhat =yq+innovation(:,cluster_assign);  % Fallback prediction
else
    % yhat =yq+innovation;  % Fallback prediction
    innovation(1:end-1)=0;
    yhat =yq+innovation;
end


end
function x=hardmax(x)
    % the max one is 1 the other is zero
    [~,idx]=max(x,[],2);
    x=zeros(size(x));
    x(sub2ind(size(x),1:size(x,1),idx))=1;
end

function [Yhat,L] = eval_nars(nars, X, p_c_given_xy)
    k = length(nars);
    for c = 1:k
        if isempty(nars{c}) || isempty(nars{c}.X)
            Yhat_c(:,:,c) = mean(y);
            continue;
        end
        
        % Adaptive prediction using k-NN bandwidths
        if isfield(nars{c}, 'kmin') && ~isempty(nars{c}.kmin)
            [Yhat_c(:, :, c),~,~,L] = NwSmoothMassCenterBatch(nars{c}.X, nars{c}.Y, nars{c}.kmin, X, nars{c}.h_i);
        else
            [Yhat_c(:, :, c),L] = NwSmoothLogGaussianBatch(nars{c}.X, nars{c}.Y, nars{c}.h, X);
        end

  
    end
    p_c_given_xy=permute(p_c_given_xy(:),[2,3,1]);
    Yhat=sum(p_c_given_xy.* Yhat_c,3);
end

function   [log_lh,mahalaD]=wdensity(X, mu, Sigma, p, sharedCov, CovType)
%WDENSITY Weighted conditional density and mahalanobis distance.
%   LOG_LH = WDENSITY(...) returns log of component conditional density
%   (weighted by the component probability) of X. LOG_LH is a N-by-K matrix
%   LOG_LH, where K is the number of Gaussian components. LOG_LH(I,J) is
%   log (Pr(point I|component J) * Prob( component J))
%
%   [LOG_LH, MAHALAD]=WDENSITY(...) returns the Mahalanobis distance in
%   the N-by-K matrix MAHALAD. MAHALAD(I,J) is the Mahalanobis distance of
%   point I from the mean of component J.

%   Copyright 2015 The MathWorks, Inc.

log_prior = log(p);
[n,d]=size(X);
k=size(mu,1);
log_lh = zeros(n,k,'like',X);
if nargout > 1
    mahalaD = zeros(n,k,'like',X);
end
logDetSigma = -Inf;
for j = 1:k
    if sharedCov
        if j == 1
            if CovType == 2 % full covariance
                [L,f] = chol(Sigma);
                diagL = diag(L);
                if (f ~= 0)|| any(abs(diagL) < eps(max(abs(diagL)))*size(L,1))
                    error(message('stats:gmdistribution:wdensity:IllCondCov'));
                end
                logDetSigma = 2*sum(log(diagL));
            else %diagonal
                L = sqrt(Sigma);
                if  any(L < eps( max(L))*d)
                    error(message('stats:gmdistribution:wdensity:IllCondCov'));
                end
                logDetSigma = sum( log(Sigma) );
            end
        end
    else %different covariance
        if CovType == 2 %full covariacne
            % compute the log determinant of covariance
            [L,f] = chol(Sigma(:,:,j) );
            diagL = diag(L);
            if (f ~= 0) || any(abs(diagL) < eps(max(abs(diagL)))*size(L,1))
                error(message('stats:gmdistribution:wdensity:IllCondCov'));
            end
            logDetSigma = 2*sum(log(diagL));
            % logDetSigma = sum(log(diagL));
        else %diagonal covariance
            L = sqrt(Sigma(:,:,j)); % a vector
            if  any(L < eps(max(L))*d)
                error(message('stats:gmdistribution:wdensity:IllCondCov'));
            end
            logDetSigma = sum(log(Sigma(:,:,j)) );
        end
    end

    if CovType == 2
        log_lh(:,j) = sum(((X - mu(j,:))/L).^2, 2);
    else %diagonal covariance
        log_lh(:,j) = sum(((X - mu(j,:))./L).^2, 2);
    end

    if nargout > 1
        mahalaD(:,j) = log_lh(:,j);
    end
    log_lh(:,j) = -0.5*(log_lh(:,j) + logDetSigma);
end
%log_lh is a N by K matrix, log_lh(i,j) is log \alpha_j(x_i|\theta_j)
log_lh = log_lh + log_prior - d*log(2*pi)/2;
% nllh=0.5*log_lh+ logDetSigma+d*log(2*pi)/2;

end



function nllh = mvnnllh_inline(param, X)
% mvnpdf_inline Compute the negative log-likelihood (NLLH) for each data point under the Gaussian distribution.
%
%   Inputs:
%       X: Data matrix (n x d).
%       param: Structure containing the Gaussian parameters:
%           - Mu: Mean vector of the Gaussian distribution.
%           - Rinv: Inverse of the Cholesky decomposition of the covariance matrix.
%           - normalize_term: Precomputed normalization term.
%
%   Output:
%       nllh: A column vector (n x 1) containing the negative log-likelihood values for each data point.
for c = length(param):-1:1

    diff = X - param{c}.Mu;  % Difference between data points and mean
    quadForm = sum((diff * param{c}.Rinv).^2, 2);  % Quadratic form (x - mu)^T Sigma^{-1} (x - mu)

    % Compute the NLLH: 0.5 * (quadForm + log(2*pi) + log(det(Sigma)))
    nllh(:,c) = 0.5 * quadForm + param{c}.normalize_term;
end
end



function y=mvnpdf_inline(X, param)
y = exp(-0.5 *sum((((X-param.Mu)*param.Rinv).^2), 2)+param.normalize_term);
end




function param=calc_norm_pdf_param(Mu, Sigma)
[R, err] = cholcov(Sigma, 0);
d=length(Mu);
param.Mu=Mu;
param.Sigma=Sigma;
param.Rinv=inv(R);
logSqrtDetSigma = sum(log(diag(R)));
param.normalize_term= + logSqrtDetSigma + d * log(2 * pi) / 2;

end


function configure_figure_theme(fig, cnars)
% Configure visual theme (dark/light)
if isfield(cnars.opt, 'black_bg') && cnars.opt.black_bg
    set(fig, 'Color', 'k');
    ax = gca;
    ax.XColor = 'w';
    ax.YColor = 'w';
    ax.ZColor = 'w';
    ax.GridColor = [0.7 0.7 0.7];
    ax.GridAlpha = 0.3;
    ax.Color = 'k';
    colorbar('Color', 'w');
end
end


function plot_potential_surface(cnars, viz_data)
% PLOT_POTENTIAL_SURFACE - Plot potential landscape as a surface
% Inputs:
%   cnars: Model structure containing potential function
%   viz_data: Existing visualization data for range determination

    % Create grid based on data range
    range=[max(viz_data(:,1))-min(viz_data(:,1)),max(viz_data(:,2))-min(viz_data(:,2))];
    x_vals = linspace(min(viz_data(:,1))-0.2*range(1), max(viz_data(:,1))+0.2*range(1), 50);
    y_vals = linspace(min(viz_data(:,2))-0.2*range(2), max(viz_data(:,2))+0.2*range(2), 50);
    [x_grid, y_grid] = meshgrid(x_vals, y_vals);
    
    % Calculate potential values
    z_grid = cnars.opt.potential(x_grid, y_grid);
    
    % Handle potential NaN/Inf values
    z_grid(isinf(z_grid)) = nan;
    z_grid = real(z_grid);
    
    % Plot surface with theme-appropriate colors
    if isfield(cnars.opt, 'black_bg') && cnars.opt.black_bg
        surf(x_grid, y_grid, z_grid, 'FaceAlpha', 0.1, ...
            'EdgeColor', 'none', 'FaceColor', [0.6 0.6 1]);
    else
        surf(x_grid, y_grid, z_grid, 'FaceAlpha', 0.3, ...
            'EdgeColor', 'none', 'FaceColor', [0.3 0.3 1]);
    end
    % % Add depth sorting for better visibility
    % [~, sort_idx] = sort(z_grid(:), 'descend');
    % x_grid = x_grid(sort_idx);
    % y_grid = y_grid(sort_idx);
    % z_grid = z_grid(sort_idx);

    % surf(x_sorted, y_sorted, z_sorted, ...
    %     'FaceAlpha', 0.3, 'EdgeColor', 'none', ...
    %     'FaceColor', [0.3 0.3 1], 'FaceLighting', 'gouraud');
    % Plot surface with theme-appropriate colors
    % if isfield(cnars.opt, 'black_bg') && cnars.opt.black_bg
    %     surf(x_grid, y_grid, z_grid, 'FaceAlpha', 0.3, ...
    %         'EdgeColor', 'none', 'FaceColor', [0.6 0.6 1]);
    % else
    %     surf(x_grid, y_grid, z_grid, 'FaceAlpha', 0.3, ...
    %         'EdgeColor', 'none', 'FaceColor', [0.3 0.3 1]);
    % end
    % Add lighting for better depth perception
    camlight('headlight');
    lighting gouraud;

    % Add colorbar with consistent theme
    % c = colorbar;
    % if isfield(cnars.opt, 'black_bg') && cnars.opt.black_bg
    %     c.Color = 'w';
    % else
    %     c.Color = 'k';
    % end
end

function color_matrix = get_cluster_colors(cluster_ids, varargin)
% GET_CLUSTER_COLORS - Create color matrix for cluster visualization
% Inputs:
%   cluster_ids: Vector of cluster indices (numeric)
%   Optional:
%     'colormap' - Colormap name or custom colormap matrix
%     'colors' - Direct color specifications (Nx3 matrix)
% Output:
%   color_matrix: Nx3 matrix of RGB colors

    % Parse optional inputs
    p = inputParser;
    addOptional(p, 'colormap', 'lines', @(x) ischar(x) || isnumeric(x));
    addOptional(p, 'colors', []);
    parse(p, varargin{:});
    
    % Get unique clusters
    unique_clusters = unique(cluster_ids);
    num_clusters = length(unique_clusters);
    
    % Handle different color specification modes
    if ~isempty(p.Results.colors)
        % Direct color specification
        if size(p.Results.colors, 1) ~= num_clusters
            warning('Color count mismatch. Using default colormap.');
            cmap = jet(num_clusters);
        else
            cmap = p.Results.colors;
        end
    else
        % Colormap-based specification
        if ischar(p.Results.colormap)
            try
                cmap_fun = str2func(p.Results.colormap);
                cmap = cmap_fun(num_clusters);
            catch
                warning('Invalid colormap name. Using jet instead.');
                cmap = jet(num_clusters);
            end
        else
            if size(p.Results.colormap, 2) ~= 3
                warning('Invalid custom colormap. Using jet instead.');
                cmap = jet(num_clusters);
            else
                cmap = p.Results.colormap;
            end
        end
    end
    
    % Create color matrix using cluster indices
    [~, cluster_idx] = ismember(cluster_ids, unique_clusters);
    color_matrix = cmap(cluster_idx, :);
end






%{
function [yq, L, dbg] = NwSmoothInline(x, y, h, xq)
if nargin < 4 || isempty(xq)
    xq = x;
end

[n, dx] = size(x);
[nq, ~] = size(xq);
y=permute(y,[1,3,2]);
if size(x, 2) > 1
    x = reshape(x, [n, 1, dx]);
end

if size(xq, 2) > 1
    xq = reshape(xq, [nq, 1, dx]);
end

h = permute(h, [dx + 2:-1:1]);

D = x - permute(xq, [2, 1, 3]);
Dkn = (gaussian_kernel(D, h));

if ~isempty(y)
    yq = permute((sum(Dkn .* y, 1) ./ sum(Dkn, 1)),[2,3,1]);
else
    yq=[];
end

dbg.s = sum(Dkn, 1);

if nargout > 1
    L = Dkn ./ sum(Dkn, 1).';
end

end

function u = gaussian_kernel(u, b)
u = u ./ b;
u = (1 / sqrt(2 * pi)) * exp(-0.5 * sum((u .^ 2), 3));
end


function [yq, L, dbg] = NwSmoothLogGaussian(x, y, h, xq)
% Log-Gaussian kernel smoother with numerical stability

if nargin < 4 || isempty(xq)
    xq = x;
end

[n, dx] = size(x);
[nq, ~] = size(xq);
y=permute(y,[1,3,2]);
% Dimension management
if size(x,2) > 1
    x = reshape(x, [n,1,dx]);
end
if size(xq,2) > 1
    xq = reshape(xq, [nq,1,dx]);
end

h = permute(h, [dx+2:-1:1]);
D = x - permute(xq, [2,1,3]);

% Log-Gaussian kernel computation
log_kn = -0.5 * sum((D./h).^2, 3);

% Numerical stability
max_log = max(log_kn, [], 1);
log_sum = log(sum(exp(log_kn - max_log), 1)) + max_log;
weights = exp(log_kn - log_sum);

% Prediction calculation
% yq = permute((sum(weights .* y, 1) ./ sum(weights, 1)),[2,3,1]);
if ~isempty(y)
    yq = permute((sum(weights .* y, 1) ./ sum(weights, 1)),[2,3,1]);
else
    yq=[];
end
% Debug outputs
dbg.s = sum(weights, 1);
if nargout > 1
    L = weights;
end
end

%}