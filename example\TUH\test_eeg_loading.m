%% Test Script for TUH and BIDS EEG Loading Functions
% This script demonstrates how to use the EEG loading functions and
% compare consistency between TUH and BIDS formats

clear; clc;

% Define file paths
tuh_file = 'aaaaaaac_s001_t000.edf';
bids_file = 'sub-000_ses-001_task-szMonitoring_run-01_eeg.edf';
eeglab_path = 'E:\code\tools\eeglab2025.0.0\';

fprintf('=== EEG Loading and Comparison Test ===\n\n');

%% Test 1: Load TUH format
fprintf('TEST 1: Loading TUH format EEG data\n');
fprintf('=====================================\n');

try
    EEG_tuh = load_tuh_eeg(tuh_file, eeglab_path);
    fprintf('✓ TUH loading successful\n\n');
catch ME
    fprintf('✗ TUH loading failed: %s\n\n', ME.message);
    return;
end

%% Test 2: Load BIDS format
fprintf('TEST 2: Loading BIDS format EEG data\n');
fprintf('====================================\n');

try
    EEG_bids = load_bids_eeg(bids_file, eeglab_path);
    fprintf('✓ BIDS loading successful\n\n');
catch ME
    fprintf('✗ BIDS loading failed: %s\n\n', ME.message);
    return;
end

%% Test 3: Compare consistency
fprintf('TEST 3: Comparing TUH vs BIDS consistency\n');
fprintf('=========================================\n');

try
    comparison_report = compare_tuh_bids_consistency(tuh_file, bids_file, eeglab_path);
    fprintf('✓ Consistency comparison completed\n\n');
catch ME
    fprintf('✗ Consistency comparison failed: %s\n\n', ME.message);
    return;
end

%% Display summary
fprintf('=== SUMMARY ===\n');
fprintf('TUH Dataset:\n');
fprintf('  File: %s\n', tuh_file);
fprintf('  Subject: %s\n', EEG_tuh.subject);
fprintf('  Channels: %d\n', EEG_tuh.nbchan);
fprintf('  Duration: %.2f seconds\n', EEG_tuh.pnts/EEG_tuh.srate);

fprintf('\nBIDS Dataset:\n');
fprintf('  File: %s\n', bids_file);
fprintf('  Subject: %s\n', EEG_bids.subject);
fprintf('  Task: %s\n', EEG_bids.task);
fprintf('  Channels: %d\n', EEG_bids.nbchan);
fprintf('  Duration: %.2f seconds\n', EEG_bids.pnts/EEG_bids.srate);
if isfield(EEG_bids, 'event') && ~isempty(EEG_bids.event)
    fprintf('  Events: %d\n', length(EEG_bids.event));
end

fprintf('\nConsistency: %s\n', ...
        iif(comparison_report.overall_consistent, 'CONSISTENT', 'INCONSISTENT'));

fprintf('\nAll tests completed successfully!\n');

%% Optional: Display channel information
fprintf('\n=== CHANNEL INFORMATION ===\n');
fprintf('TUH Channels: %s\n', strjoin({EEG_tuh.chanlocs.labels}, ', '));
fprintf('BIDS Channels: %s\n', strjoin({EEG_bids.chanlocs.labels}, ', '));

%% Optional: Display event information (if available)
if isfield(EEG_bids, 'event') && ~isempty(EEG_bids.event)
    fprintf('\n=== EVENT INFORMATION ===\n');
    event_types = {EEG_bids.event.type};
    unique_types = unique(event_types);
    
    fprintf('Event types found in BIDS data:\n');
    for i = 1:length(unique_types)
        count = sum(strcmp(event_types, unique_types{i}));
        fprintf('  %s: %d events\n', unique_types{i}, count);
    end
    
    % Display first few events
    fprintf('\nFirst 5 events:\n');
    for i = 1:min(5, length(EEG_bids.event))
        fprintf('  Event %d: %s at %.2f seconds\n', i, ...
                EEG_bids.event(i).type, ...
                (EEG_bids.event(i).latency-1)/EEG_bids.srate);
    end
end

% Helper function
function result = iif(condition, true_val, false_val)
    if condition
        result = true_val;
    else
        result = false_val;
    end
end
