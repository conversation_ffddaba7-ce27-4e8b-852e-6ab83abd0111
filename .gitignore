# Ignore specific folders and files
dataset
datasets
helpfiles
images
graphics
utilities_help
# doc
backup
result
figure
fig
data
temp
external/eeglab*
.vscode
codegen
project
tests-Jenkins

# Ignore specific file types
*.RData
*.asv
*.fig
*.bmp
*.jpg
*.mat
*.h5
*.set
*.plg
*.cdc
*.cmm
*.inf
*.pat
*.mrk
*.h5
*.rar
*.gz
*.tgz
*.bz2
*.eps
*.zip
*.7z
~$*.docx
~$*.rtf
*.csv
Demo/outputData/testdata_combat_parametric_adjusted_matlab.csv
_gsdata_
*.m~
.Rhistory
.RDataTmp
.RData
*.pptx
*.png
*.html
*.R~
*.pyc
*.log
*.mldatx
__pycache__
*.json
*.prj
!image/readme/*.png
*.slxc
slprj
*.acq
*.eeg
*.hc
*.hist
*.infods
*.bak
*.meg4
*.newds
*.res4
external/meth/meth/examples/11post_PlastP_20130226_01.ds/BadChannels
external/meth/meth/examples/11post_PlastP_20130226_01.ds/hz.ds/BadChannels
*.mri
external/meth/meth/lowlevel/sphe0
*.cfg
*.cls
*.out
*.ffs_db
demo/tempCodeRunnerFile.py
*.tmp
*.mlappinstall
*.gif
*.svg
*.woff
*.ttf
*.eot
*.pdf
*.zap
*.aux
.DS_Store
*.exe
# *.dll
*.tif
*.mlx
*.mlapp
*.act
*.txt
*.dat
*.ffs_lock
*.wav
/.idea
