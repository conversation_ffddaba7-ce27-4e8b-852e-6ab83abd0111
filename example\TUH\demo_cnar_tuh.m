% Load both datasets
bids_file='sub-000_ses-001_task-szMonitoring_run-01_eeg.edf';

EEG = load_bids_eeg(bids_file);
%%

% Apply filtering if requested using EEGLAB tools
filter_data=true;
if filter_data
    fprintf('Applying EEGLAB bandpass filter (1-50 Hz)...\n');
    % Use EEGLAB's pop_eegfiltnew for better filtering with minimal distortion
    % High-pass filter at 1 Hz
    % X = pop_eegfiltnew(EEG, EEG.srate, 1, [], 0, [], 0);
    % Low-pass filter at 50 Hz  
    % X = pop_eegfiltnew(X, EEG.srate, [], 50, 0, [], 0);
    EEG = pop_eegfiltnew(EEG, 1, 50);
    fprintf('EEGLAB filtering completed.\n');
end
% Apply downsampling to 100Hz using EEGLAB tools
downsample_data = true;
target_fs = 100; % Target sampling frequency in Hz
if downsample_data && EEG.srate > target_fs
    fprintf('Downsampling from %.1f Hz to %.1f Hz using EEGLAB...\n', EEG.srate, target_fs);
    % Use EEGLAB's pop_resample for proper anti-aliasing and resampling
    EEG = pop_resample(EEG, target_fs);
    fprintf('EEGLAB resampling completed. New sampling rate: %.1f Hz, New data size: %d channels x %d samples\n', EEG.srate, n_channels, n_samples);
end
X = double(EEG.data);
[n_channels, n_samples] = size(X); % Update dimensions

%%
figure(301)
plot(X');
%%
x=X(5,:)';
%%
k=2;
opt.use_threshold=false;
opt.p=20;
[cnars] = fit_cnar_unsup(x,k,opt);
cnars.Fs=EEG.srate;
%%
figure(101)
visualize_cnars_train(cnars)


%%
% Estimate potential landscape
sigma=0.2;
cnars.opt.issoft=true;

result = estimate_potential_cnar(cnars, 100, cnars.p*10, sigma, 1000);


figure(102); clf;
% Visualize with optional trajectory
plot_potential_pca(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off


figure(103); clf;
% Visualize with optional trajectory
plot_potential_cnar(result, []);
set(gca, 'SortMethod', 'depth');
% set(gca, 'SortMethod', 'childorder');
view(2)
axis off

%%
cnars.opt.vis_y=true;
[cnars] = gen_cluster_lpr_ts_all_random(cnars, 'residual', length(x),true);
