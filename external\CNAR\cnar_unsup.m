

function [cnars] = cnar_unsup(X, p, k, max_iter, opt)
% cluster_nar_gmm_nw_unsup: Unsupervised GMM-NW clustering for multichannel data
% Inputs:
%   X - Multichannel time series [N×Nch]
%   p - Embedding dimension
%   h - Bandwidth(s) for NW regression
%   k - Number of clusters (optional)
%   max_iter - Maximum iterations
%   opt - Options struct
% Output:
%   cnars - Model structure with clustering results

if nargin < 5 || isempty(opt)
    opt = struct();
end
opt = set_defaults(opt, 'use_threshold', true, 'use_gpu', true, 'isadaptive', true, 'issoft', false);
rng(0); % For deterministic initialization

% 1. Multichannel Hankel Embedding
Nch = size(X, 2);
H = mvts2hankel(X, p);
d=Nch*p;
inputs_full = H(1:end - 1, :);
outputs_full = H(2:end, :);
% [N_full, ~] = size(inputs_full);

% 2. GPU Conversion
if opt.use_gpu
    [inputs_full, outputs_full, p, k] = ...
        convert_to_gpuarray(inputs_full, outputs_full, p, k);
end

% 3. Cluster Count Estimation
if isempty(k)
    k_candidates = 1:3;
    k = estimate_optimal_k(inputs_full, k_candidates);
    fprintf('Estimated k = %d\n', k);
end

% 4. GMM Initialization with Multichannel Support
if k>1
    options = statset('MaxIter', 2000, 'Display', 'off');
    if opt.use_threshold
        initial_idx = hilbert_init_clustering_mv(X, inputs_full, p);%plot([outputs_full(:,1),initial_idx])
        [mu_init, Sigma_init, p_c] = init_base_clusters(inputs_full, initial_idx);
        Sigma_init = reg_spd(Sigma_init);
        gmm = fitgmdist(inputs_full, k, 'Options', options, ...
            'Start', struct('mu', mu_init, 'Sigma', Sigma_init, 'ComponentProportion', p_c), ...
            'RegularizationValue', 1e-3);
    else
        gmm = fitgmdist(gather(inputs_full), gather(k), 'Options', options, ...
            'RegularizationValue', 1e-1, 'Replicates', 5);
    end
    idx_train = cluster(gmm, inputs_full);

else
    gmm=[];
    idx_train=ones(size(inputs_full,1),1);
end

% 5. Main EM Loop
joint_NLL_history = zeros(max_iter, 1, 'like', outputs_full);
for iter = 1:max_iter
    disp(['loop: ',num2str(iter)])
    % A. Update NW Models
    nars = update_nw_models(inputs_full, outputs_full, idx_train, opt);
    if max_iter==1 
        continue;
    end
    if k==1
        break;
    end

    % B. Assign Clusters via NW Likelihood
    [new_idx, log_likelihood] = assign_clusters(nars, inputs_full, outputs_full);
    joint_NLL_history(iter) = -sum(log_likelihood, 'all', 'omitnan');
    
    % C. Update GMM
    if k>1
        gmm = update_gmm(inputs_full, new_idx, k, options);
    else
        gmm=[];
    end
    % D. Check Convergence
    if iter > 1 && all(new_idx == idx_train)
        fprintf('Converged at iteration %d\n', iter);
        break;
    end
    idx_train = new_idx;
end

% 6. Final Model Evaluation
[sigma_y, sigma_y_c, res, res_c] = ...
    eval_nars(nars, inputs_full, outputs_full, idx_train);

% 7. Final Model Evaluation
if k > 1
    % Compute cluster probabilities using GMM posterior
    cluster_probs = gmm.posterior(inputs_full);
else
    % Single cluster, all probabilities are 1
    cluster_probs = ones(size(inputs_full, 1), 1, 'like', inputs_full);
end

% 8. Output Structure
if k>1
cnars = struct(...
    'x', X, 'nars', {nars}, 'gmm', gmm, 'k', k, ...
    'p', p, 'Nch', Nch,'d',d, 'p_c', gmm.PComponents, ...
    'sigma_y', sigma_y, 'sigma_y_c', sigma_y_c, ...
    'res', res, 'res_c', res_c, ...
    'idx_train', [nan(p, 1); idx_train],...
    'joint_NLL_history',joint_NLL_history, ...
     'cluster_probs', cluster_probs,'opt',opt);
else
    cnars = struct(...
    'x', X, 'nars', {nars}, 'gmm', gmm, 'k', k, ...
    'p', p, 'Nch', Nch,'d',d, 'p_c', 1, ...
    'sigma_y', sigma_y, 'sigma_y_c', sigma_y_c, ...
    'res', res, 'res_c', res_c, ...
    'idx_train', [nan(p, 1); idx_train],...
    'joint_NLL_history',joint_NLL_history, ...
     'cluster_probs', cluster_probs,'opt',opt);
end
end

%% Multichannel Helper Functions
function initial_idx = hilbert_init_clustering_mv(X, H, p)
% Hilbert-based initialization for multichannel data
analytic_signal = hilbert(X);
inst_amp = abs(analytic_signal);
ch_norms = sqrt(sum(inst_amp.^2, 2)); % Channel-agnostic amplitude

threshold = quantile(ch_norms, 0.75);
initial_idx = zeros(size(H, 1), 1);

for i = 1:size(H, 1)
    win_amp = ch_norms(i:i+p-1);
    initial_idx(i) = 2 - (sum(win_amp > threshold) <= p/2);
end
end

function nars = update_nw_models(inputs, outputs, idx, opt)
% Update NW models for each cluster with multichannel outputs
k = max(idx);
nars = cell(k, 1);


for c = 1:k
    mask = idx == c;
    if sum(mask) < 2, continue; end

    if opt.isadaptive
        % Use adaptive bandwidth selection with k-NN
        klist = unique(ceil(linspace(1, round(size(inputs(mask,:),1)/20), 100)))';
        [~, kmin, h_i, ~] = NwSmoothMassCenterGCV(...
            inputs(mask,:), outputs(mask,:), klist, inputs(mask,:));
        nars{c} = struct('X', inputs(mask,:), 'Y', outputs(mask,:), 'h',[],'kmin', kmin, 'h_i', h_i);
    else
        % Global bandwidth selection
        hlist = get_hList(100, [(max(inputs, [], 'all') - min(inputs, [], 'all')) / 100, ...
            (max(inputs, [], 'all') - min(inputs, [], 'all')) / 1], @logspace);
        [~, gcv] = NwSmoothGCVOriginalScaleLogGaussian(...
            inputs(mask,:), outputs(mask,:), hlist, inputs(mask,:));
        nars{c} = struct('X', inputs(mask,:), 'Y', outputs(mask,:), 'h', gcv.hmin,'kmin', [], 'h_i', []);
    end
end


end
%{
function [new_idx, llh] = assign_clusters(nars, inputs, outputs)
% Multichannel cluster assignment via NW likelihood
k = numel(nars);
[N, Nch] = size(outputs);
llh = zeros(N, k, 'like', outputs);

for c = 1:k
    if isempty(nars{c}), continue; end
    
    % Multichannel predictions
    yhat = NwSmoothBatch(nars{c}.X, nars{c}.Y, nars{c}.h, inputs);
    res = outputs - yhat;
    
    % Channel-wise likelihood aggregation
    % sigma = std(res, 1) + 1e-8;
    % llh(:,c) = mean(sum(-0.5*(res.^2 ./ sigma.^2) - log(sigma), 2),2);
    llh(:,c)=-mean(abs(res).^2,2);
end

[~, new_idx] = max(llh, [], 2);
end
%}
function [new_idx, llh] = assign_clusters(nars, inputs, outputs)
    k = numel(nars); 
    [N, Nch] = size(outputs); 
    llh = zeros(N, k, 'like', outputs);
    
    for c = 1:k
        if isempty(nars{c}), continue; end

        % Adaptive prediction using k-NN bandwidths
        if isfield(nars{c}, 'kmin')
            yhat = NwSmoothMassCenterBatch(nars{c}.X, nars{c}.Y, nars{c}.kmin, inputs, nars{c}.h_i);
        else
            yhat = NwSmoothLogGaussianBatch(nars{c}.X, nars{c}.Y, nars{c}.h, inputs);
        end

        % Compute residuals and log-likelihood
        res = outputs - yhat;
        llh(:,c) = -mean(abs(res).^2, 2);
    end
    [~, new_idx] = max(llh, [], 2);
end



function gmm = update_gmm(inputs, idx, k, options)
% GMM update with regularization
[mu, Sigma, p_c] = deal(zeros(k, size(inputs,2)), ...
    zeros(size(inputs,2), size(inputs,2), k), zeros(1,k));

for c = 1:k
    cluster_data = inputs(idx == c,:);
    mu(c,:) = mean(cluster_data);
    Sigma(:,:,c) = cov(cluster_data);
    p_c(c) = size(cluster_data,1)/size(inputs,1);
end
Sigma = reg_spd(Sigma);

gmm = fitgmdist(inputs, k, 'Start', struct('mu',mu, 'Sigma',Sigma, 'ComponentProportion',p_c), ...
    'RegularizationValue', 1e-1, 'Options', options);
end


function [sigma_y,sigma_y_c,res,res_c] = eval_nars(nars, X, y, idx_true)
    k = length(nars);
    n = size(X, 1);
    Nch = size(y, 2);
    yhat_c = zeros(n,Nch, k, 'like', y);
    sigma_y_c = zeros(1,Nch,k, 'like', y);
    res_c = zeros(n, Nch, k, 'like', y);

    % Determine batch size based on available memory
    % [~, freemem] = test_gpu([], [], false);
    % elements_per_point = size(nars{1}.X, 1) * size(X, 2); % n_train * dx

    for c = 1:k
        if isempty(nars{c}) || isempty(nars{c}.X)
            yhat_c(:,:,c) = mean(y);
            sigma_y_c(c,:) = std(y,"omitmissing") + 1e-8;
            continue;
        end
        
        % Adaptive prediction using k-NN bandwidths
        if isfield(nars{c}, 'kmin')
            yhat_c(:, :, c) = NwSmoothMassCenterBatch(nars{c}.X, nars{c}.Y, nars{c}.kmin, X, nars{c}.h_i);
        else
            yhat_c(:, :, c) = NwSmoothLogGaussianBatch(nars{c}.X, nars{c}.Y, nars{c}.h, X);
        end

        % Compute sigma using training residuals
        % yhat = NwSmoothBatch(nars{c}.X, nars{c}.Y, nars{c}.h, X);
        res_c(:, :, c) = y - yhat_c(:, :, c);
        sigma_y_c(:,:,c) = std(res_c(:, :, c),1,"omitmissing") + 1e-8;
    end
    
    % res = res_c(sub2ind(size(res_c), (1:n)', idx_true));
    % Extract cluster indices for the current batch
    idx_true_slice = idx_true(end - n + 1 : end,1); % Last n elements

    % Generate expanded indices for all sample-channel pairs
    i_idx = repelem((1:n)', Nch);      % Row indices: [1;1;2;2;...]
    c_idx = repmat((1:Nch)', n, 1);   % Column indices: [1;2;1;2;...]
    cluster_idx = repelem(idx_true_slice, Nch); % Cluster indices

    % Convert to linear indices
    lin_indices = sub2ind(size(res_c), i_idx, c_idx, cluster_idx);

    % Extract residuals and reshape to [n × Nch]
    res = reshape(res_c(lin_indices), n, Nch);
    
    sigma_y = std(res,1,"omitmissing");
end


function [mu_init, Sigma_init, p_init] = init_base_clusters(inputs_norm, initial_idx)
k_base = 2;
mu_init = zeros(k_base, size(inputs_norm,2), 'like', inputs_norm);
Sigma_init = zeros(size(inputs_norm,2), size(inputs_norm,2), k_base, 'like', inputs_norm);
p_init = zeros(1,k_base,'like',inputs_norm);
num_segments = size(inputs_norm,1);
for c = 1:k_base
    cluster_data = inputs_norm(initial_idx == c,:);
    if ~isempty(cluster_data)
        mu_init(c,:) = mean(cluster_data,1);
        Sigma_init(:,:,c) = cov(cluster_data);
        p_init(c) = sum(initial_idx == c)/num_segments;
    else
        mu_init(c,:) = mean(inputs_norm,1);
        Sigma_init(:,:,c) = cov(inputs_norm);
        p_init(c) = 1/k_base;
    end
end
end



%{
function [yq, dbg] = NwSmoothBatch(x, y, h, xq)
% Process NW smoothing in batches to avoid memory overflow
    
    [n, dx] = size(x);
    nq = size(xq, 1);
    yq = zeros(nq, size(y,2), 'like', y);
    x_exp = permute(x, [1,3,2]);
    xq_exp = permute(xq,[3,1,2]);
    y=permute(y,[1,3,2]);
    % Process query points in batches
    if isgpuarray(x)
        [bsize] = bytesize(1, 'b', single(1));
        [~,freemem]=test_gpu([],[],true);
    else
        [bsize] = bytesize(1, 'b', underlyingType(x(1)));
        [~, freemem] = test_memory();
    end
    % batch_size = max(1, floor(freemem / (elements_per_point * 4))); % heuristic
    num_free = max(1, floor(freemem / (bsize.b))); % heuristic

    num_per_batch=(num_free/(n*dx));
    num_batches = ceil(nq / num_per_batch);
    for i = 1:num_batches
        batch_idx = int64((i-1)*num_per_batch + 1 : min(i*num_per_batch, nq));
        xq_batch = xq_exp(:,batch_idx, :);
        
        D = x_exp - xq_batch;
        
        % Compute kernel weights and predictions
        Dkn = gaussian_kernel(D, h);
        sum_weights = sum(Dkn, 1);
        valid = sum_weights > eps('single');
        yq_batch = sum(Dkn .* y, 1) ./ (sum_weights + ~valid * eps('single'));
        yq(batch_idx,:) = gather(yq_batch); % Move to CPU if using GPU
        
        % Clear temporary GPU arrays
        clear D Dkn sum_weights yq_batch;
    end
    dbg.s = [];
end

function u = gaussian_kernel(u,b)
u = u./b;
u = (1/sqrt(2*pi)) * exp(-0.5 * sum((u.^2),3));
end
%}